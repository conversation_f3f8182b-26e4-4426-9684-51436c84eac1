# VTJ低代码设计器入门系列（一）：项目工程搭建和配置

VTJ是一款基于 Vue3 + TypeScript 的低代码开发工具，内置可视化设计器，可用来开发Vue3的前端应用。

官方提供了开箱即用的项目脚手架，可以快速创建基于VTJ低代码的开发项目工程，并默认最佳实践的配置，非常适用新项目开发。

:::warning 开发环境要求
VTJ 使用了最新的 Vue3 生态技术栈，要求 Node 版本必须是 v20+， 建议使用 nvm 切换 Node 版本
:::

## 使用脚手架创建项目

打开终端工具输入命令运行

```sh
npm create vtj@latest -- -t app
```

如npm访问不了，可以尝试使用镜像：

```sh
npm create vtj@latest --registry=https://registry.npmmirror.com -- -t app
```

项目创建完成后进入，按提示进入目录 安装依赖 和 启动开发环境

```sh
npm run setup && npm run dev
```

看到以下页面，项目已正常运行，你可以开始使用设计器以低代码的方式开发应用了。

![startup](../../assets//startup.png)

按提示点击开始设计按钮，即可打开设计器。

![p1](../../assets/p1.png)

## 项目工程配置

项目开发工程依赖 `@vtj/cli` , 在 vite.config.ts 通过 createViteConfig 及 createDevTools 的 vite 插件对项目设置工程化配置。

默认配置示例

```ts
import { createViteConfig } from '@vtj/cli';
import { createDevTools } from '@vtj/local';
import proxy from './proxy.config';
export default createViteConfig({
  proxy,
  plugins: [createDevTools()]
});
```

### createViteConfig 参数选项

```ts
/**
 * createViteConfig函数参数
 */
export interface CreateViteConfigOptions {
  /**
   * 打印最终 vite.config
   */
  debug?: boolean;

  /**
   * 库模式
   */
  lib?: boolean;

  /**
   * 开发或生产环境服务的公共基础路径
   */
  base?: string;

  /**
   * 打包输出文件夹
   */
  outDir?: string;

  /**
   * 清空输出目录
   */
  emptyOutDir?: boolean;

  /**
   * 自定义配置 vite 配置
   */
  defineConfig?: (config: UserConfig) => UserConfigExport;

  /**
   * 开发端口
   */
  port?: number;

  /**
   * 预览端口
   */
  previewPort?: number;

  /**
   * 主机头
   */
  host?: string;

  /**
   * 开启https
   */
  https?: boolean;

  /**
   * 代理
   */
  proxy?: ProxyConfig;

  /**
   * Vite 服务器默认会忽略对 .git/ 和 node_modules/ 目录的监听。如果你需要对 node_modules/ 内的包进行监听
   */
  watchModules?: string[];

  /**
   * 别名
   */
  alias?: Record<string, string>;

  /**
   * 生成定义文件, 在库模式有效
   */
  dts?: boolean;

  /**
   * 定义文件输出文件夹
   */
  dtsOutputDir?: string;

  /**
   * 适配传统浏览器, 在非库模式下有效
   */
  legacy?: boolean;

  /**
   * 编译目标浏览器
   */
  targets?: string | string[];

  /**
   * 编译语法版本 esnext / es2015
   */
  buildTarget?: string;

  /**
   * 设置 polyfills，在非库模式下有效，默认为true
   */
  polyfills?: boolean | string[];

  /**
   *  lib 入口文件
   */
  entry?: string;

  /**
   * 不打包的依赖
   */
  external?: string[];

  /**
   * 从全局引用的依赖
   */
  externalGlobals?: Record<string, string>;

  /**
   * 库模式在umd文件导出变量名称
   */
  library?: string;

  /**
   * 库模式输入格式
   */
  formats?: string[];

  /**
   * 库模式编译输出文件名
   */
  libFileName?: string;

  /**
   * 自定义 chunk name, 在库模式无效
   */
  manualChunks?: (id: string) => string | void;

  /**
   * 强制更新依赖
   */
  force?: boolean;

  /**
   * 预构建依赖
   */
  optimizeDeps?: string[];

  /**
   * 生成报告
   */
  visualizer?: boolean;

  /**
   * 插件
   */
  plugins?: PluginOption[];

  /**
   * 开启babel
   */
  babel?: boolean;

  /**
   *  开启生成版本号文件
   */
  version?: boolean;

  /**
   * 开启 unplugin-element-plus
   */
  elementPlus?: boolean | Record<string, any>;

  /**
   * 静态服务目录
   */
  staticDirs?: Array<string | StaticPluginOption>;

  /**
   * 打包复制静态目录
   */
  copyStatic?: boolean;

  /**
   * 多页面模式
   */
  pages?: Record<string, string>;

  /**
   * 生成 loading html
   */
  loading?: boolean;

  /**
   * 环境配置文件目录位置
   */
  envPath?: string;

  /**
   * 开启 nodePolyfills
   * https://github.com/davidmyersdev/vite-plugin-node-polyfills
   */
  node?: boolean | PolyfillOptions;

  /**
   * 类库导出名称模式， 默认 auto
   * https://rollupjs.org/configuration-options/#output-exports
   */
  exports?: 'auto' | 'default' | 'named' | 'none';

  /**
   * 编译完成时触发回调函数
   * @param error
   * @returns
   */
  buildEnd?: (error?: any) => void;

  /**
   * 默认情况下，Vite 会在构建阶段将 publicDir 目录中的所有文件复制到 outDir 目录中。可以通过设置该选项为 false 来禁用该行为。
   */
  copyPublicDir?: boolean;

  /**
   * 开启 VueDevTools
   */
  devtools?: boolean | VitePluginVueDevToolsOptions;

  /**
   * CDN配置
   */
  cdn?: CdnPluginOptions;
}
```

### 开启项目二级目录

项目配置 vite.config.ts 的 base 参数，设置项目路径，需要同时配置 createDevTools 的 staticBase 参数选项

```ts
import { createViteConfig } from '@vtj/cli';
import { createDevTools } from '@vtj/pro/vite';
const basePath = '/lowcode/';
export default createViteConfig({
  base: basePath,
  elementPlus: false,
  plugins: [
    createDevTools({
      staticBase: basePath
    })
  ]
});
```
