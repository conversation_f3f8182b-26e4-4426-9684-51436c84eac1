@use 'core' as *;

@include b(attachment) {
  .el-upload-list__item {
    transition: none;
  }
  @include when(disabled) {
    .el-upload {
      display: none;
    }
  }

  @include when(not-add) {
    .el-upload {
      display: none;
    }
  }

  @include when(pointer) {
    .el-upload-list__item {
      cursor: pointer;
    }
    .el-upload-list__item-actions {
      cursor: pointer !important;
    }
  }

  @include m(card) {
    @include e(item) {
      width: 100%;
      height: 100%;
      @include when(selected) {
        background-color: var(--el-color-primary-light-8);
        border: 2px solid var(--el-color-primary);
        border-radius: 6px;
      }
    }

    @include e(wrapper) {
      width: 100%;
      height: 100%;
    }

    @include e(upload) {
      width: 100%;
      height: 100%;
      font-size: 28px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: var(--el-text-color-regular);
    }

    @include when(small) {
      .el-upload-list--picture-card {
        --el-upload-list-picture-card-size: 100px;
      }
      .el-upload--picture-card {
        --el-upload-picture-card-size: 100px;
      }
    }

    @include when(large) {
      .el-upload-list--picture-card {
        --el-upload-list-picture-card-size: 200px;
      }
      .el-upload--picture-card {
        --el-upload-picture-card-size: 200px;
      }
    }

    .el-upload-list__item-name {
      width: 100%;
      position: absolute;
      left: 0;
      bottom: 0;
      text-align: center;
      padding: 3px 5px;
      background-color: var(--el-overlay-color-lighter);
      color: var(--el-color-info-light-9);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 12px;
      z-index: 1;
      cursor: default !important;
      &:hover {
        color: var(--el-color-info-light-9) !important;
      }
    }
    .el-upload-list__item-url {
      display: none;
    }
    .el-upload-list__item {
      &:hover {
        .el-upload-list__item-name {
          display: block !important;
        }
      }
    }

    .el-upload-list__item-thumbnail {
      &.is-icon {
        padding: 20px;
        box-sizing: border-box;
      }
    }
  }

  @include m(list) {
    .el-upload-list__item-actions {
      .el-icon {
        font-size: 16px;
      }
    }

    @include e(item) {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 5px;
      box-sizing: border-box;
      @include when(selected) {
        background-color: var(--el-color-primary-light-8);
        border-radius: 6;
      }
    }
    @include e(wrapper) {
      flex-grow: 1;
      display: flex;
    }
    @include e(item-name) {
      display: none;
    }

    @include when(small) {
      .el-upload {
        height: 30px;
      }
      .el-upload-list__item {
        height: 30px;
      }
      .el-upload-list__item-thumbnail {
        height: 24px;
        width: 24px;
      }
      .el-upload-list__item-actions {
        .el-icon {
          font-size: 14px;
        }
      }
    }

    @include when(large) {
      .el-upload {
        height: 60px;
      }
      .el-upload-list__item {
        height: 60px;
      }
      .el-upload-list__item-thumbnail {
        height: 50px;
        width: 50px;
      }
      .el-upload-list__item-actions {
        .el-icon {
          font-size: 18px;
        }
      }
    }

    .el-upload {
      width: 100%;
      align-items: center;
      background-color: var(--el-fill-color-lighter);
      border: 1px dashed var(--el-border-color-darker);
      border-radius: 6px;
      box-sizing: border-box;
      cursor: pointer;
      display: inline-flex;
      height: var(--el-upload-picture-card-size);
      justify-content: center;
      vertical-align: top;
      height: 40px;
      font-size: 20px;
    }
    @include e(upload) {
    }

    .el-upload-list {
      width: 100%;
      display: flex;
      flex-direction: column-reverse;
    }
    .el-upload-list__item {
      width: 100%;
      margin: 0;
      height: 40px;
      padding: 0 !important;
      border: none;
      border-bottom: 1px solid var(--el-border-color-lighter);
      border-radius: 0;
    }
    .el-upload-list__item-actions {
      opacity: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      > span {
        padding: 5px;
        cursor: pointer;
        &:hover {
          color: var(--el-color-primary);
        }
      }
    }
    .el-upload-list__item-thumbnail {
      height: 30px;
      width: 30px;
      margin-right: 8px;
    }
    .el-upload-list__item-delete {
      display: block;
      position: static;
    }

    .el-upload-list__item-name {
      cursor: default !important;

      &:hover {
        color: var(--el-text-color-regular) !important;
      }
    }
  }
}
