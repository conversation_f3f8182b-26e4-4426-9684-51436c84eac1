# XCaptcha 图形验证码


## 示例


### 基础用法


:::preview
demo-preview=../examples/ui/captcha/base.vue
::: 



## API

### 属性 

| 属性名      | 说明         | 类型       | 默认值 |
| ----------- | ------------ | ---------- | ------ |
| src         | 图片加载函数 | `function` | -      |
| maxlength   | 验证码长度   | `number`   | 4      |
| placeholder | 占位文本     | `string`   | -      |
| validate    | 校验规则     | `function` | -      |
| size        | 尺寸         | `string`   | -      |
