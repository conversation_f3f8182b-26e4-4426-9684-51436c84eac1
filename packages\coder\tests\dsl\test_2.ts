export const test_2 = {
  name: 'Bbb',
  locked: false,
  inject: [],
  state: {},
  lifeCycles: {},
  methods: {},
  computed: {},
  watch: [],
  css: '',
  props: [],
  emits: [],
  slots: [],
  dataSources: {},
  __VTJ_BLOCK__: true,
  __VERSION__: '1753066559638',
  id: '235w0t1w',
  nodes: [
    {
      id: '17zuxu31',
      name: 'ElTable',
      from: 'element-plus',
      invisible: false,
      locked: false,
      children: [
        {
          id: '27zuxu31',
          name: 'ElTableColumn',
          from: 'element-plus',
          invisible: false,
          locked: false,
          children: [
            {
              id: '47zuxu31',
              name: 'div',
              from: '',
              invisible: false,
              locked: false,
              slot: {
                name: 'default',
                params: [],
                scope: 'scope'
              },
              children: '容器文本内容示例',
              props: {},
              directives: [],
              events: {}
            }
          ],
          props: {
            prop: 'date',
            label: 'Date'
          },
          directives: [],
          events: {}
        }
      ],
      props: {
        data: {
          type: 'JSExpression',
          value:
            "[\n  {\n    date: '2016-05-03',\n    name: '<PERSON>',\n    address: 'No. 189, Grove St, Los Angeles'\n  }\n]"
        }
      },
      directives: [],
      events: {}
    }
  ]
};
