# XVerify 短信验证码


## 示例


### 基础用法


:::preview
demo-preview=../examples/ui/verify/base.vue
::: 



## API

### 属性 

| 属性名      | 说明           | 类型       | 默认值 |
| ----------- | -------------- | ---------- | ------ |
| api         | 发送验证码接口 | `function` | -      |
| seconds     | 重发倒数秒数   | `number`   | -      |
| maxlength   | 验证码长度     | `number`   | -      |
| placeholder | 提示文本       | `string`   | -      |
| size        | 尺寸           | `string`   | -      |

