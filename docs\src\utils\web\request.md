# 网络请求



| 函数名                      | 描述 | 类型 | 参数      | 返回值 |
| --------------------------- | ---- | ---- | --------- | ------ |
| Request                     | -    | -    | -         | -      |
| createRequest               | -    | -    | `options` | -      |
| request                     | -    | -    | -         | -      |
| createApi                   | -    | -    | -         | -      |
| createApis                  | -    | -    | -         | -      |
| useApi                      | -    | -    | -         | -      |
| axios,                      | -    | -    | -         | -      |
| LOCAL_REQUEST_ID,           | -    | -    | -         | -      |
| type AxiosRequestConfig,    | -    | -    | -         | -      |
| type AxiosResponse,         | -    | -    | -         | -      |
| type RawAxiosRequestHeaders | -    | -    | -         | -      |



