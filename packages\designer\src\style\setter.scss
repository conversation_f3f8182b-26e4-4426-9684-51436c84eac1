@use 'core' as *;

@include b(setter) {
  align-items: center;
  .el-form-item__label {
    .el-tooltip__trigger {
      border-bottom: 1px dashed var(--el-color-primary);
      cursor: help;
    }
    span {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  &:hover {
    .el-form-item__label {
      color: var(--el-color-primary);
    }
  }

  .el-select {
    display: block;
  }

  @include e(content) {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    align-items: center;
  }
  @include e(input) {
    flex-grow: 1;
  }

  @include e(action) {
    font-size: 14px;
    white-space: nowrap;
    .el-dropdown.is-disabled {
      opacity: 0.6;
      i {
        cursor: not-allowed;
      }
    }
    i {
      cursor: pointer;
      color: var(--el-text-color-secondary);
      background-color: var(--el-color-primary-light-9);
      margin-left: 3px;
      padding: 4px;
      border-radius: 4px;
      &:hover {
        color: var(--el-color-primary);
        background-color: var(--el-color-primary-light-9);
      }
      &.is-active {
        color: var(--el-color-primary);
        background-color: var(--el-color-primary-light-9);
      }
    }
  }
}

@include b(setter-switch) {
  .is-placeholder {
    opacity: 0;
  }
}

@include b(expression-setter) {
  .el-input__inner {
    text-align: center;
  }
}

@include b(icon-setter) {
  .el-input-group__prepend {
    background-color: var(--el-fill-color-blank);
  }
  @include e(list) {
    display: inline-block;
    flex-grow: 1;
    height: 1px;
    overflow: auto;
    position: relative;
    margin: 10px 0;

    .x-icon {
      width: 50px;
      height: 50px;
      margin: 5px;
      font-size: 20px;
      border: 1px dashed var(--el-border-color);
      border-radius: 4px;
      text-align: center;
      line-height: 54px;
      &:hover {
        border: 1px solid #409eff;
        cursor: pointer;
      }
      &.is-active {
        background-color: #409eff;
        color: #fff;
        border: none;
      }
      & + .x-icon {
        margin-left: 5px;
      }
    }
  }

  @include e(content) {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}

@include b(json-setter) {
  &.is-active {
    .el-input__suffix {
      color: var(--el-color-primary);
    }
    .el-input__wrapper {
      background-color: var(--el-color-primary-light-9);
      box-shadow: 0 0 0 1px
        var(--el-color-primary-light-7, var(--el-color-primary-light-7)) inset;
    }
    .el-input__inner {
      color: var(--el-color-primary);
    }
  }
}

@include b(number-setter) {
  width: 100% !important;
}

@include b(function-setter) {
  @include when(active) {
    .el-input__suffix {
      color: var(--el-color-primary);
    }
    .el-input__wrapper {
      background-color: var(--el-color-primary-light-9);
      box-shadow: 0 0 0 1px
        var(--el-color-primary-light-7, var(--el-color-primary-light-7)) inset;
    }
    .el-input__inner {
      color: var(--el-color-primary);
    }
  }
}

@include b(radio-setter) {
  position: relative;
  top: -1px;

  .is-active {
    .x-icon img {
      filter: invert(1);
    }
  }
}

@include b(tag-setter) {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  .x-icon img {
    opacity: 0.8;
    position: relative;
    top: -1px;
  }

  > span {
    padding: 1px 2px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    height: 20px;
    cursor: pointer;
    border-radius: 2px;
    border: 1px solid transparent;
    @include when(active) {
      background-color: var(--el-color-primary-light-7);
      border: 1px solid var(--el-color-primary);
    }
    & + span {
      margin-left: 8px;
    }
  }
}

@include b(image-setter) {
  @include m(addable) {
    .el-upload {
      display: block !important;
    }
  }

  @include e(img) {
    width: 100%;
    height: 100%;
    display: block;
  }

  @include e(plus) {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 28px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .el-upload-list {
    --el-upload-list-picture-card-size: 80px;
  }

  .el-upload {
    width: 80px;
    height: 80px;
    border: 1px dashed var(--el-border-color);
    border-radius: 4px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    display: none;
    &:hover {
      border-color: var(--el-color-primary);
    }
  }
  .el-upload-list__item-preview {
    display: none !important;
  }
  .el-upload-list__item-delete {
    margin-left: 0 !important;
  }
  .el-upload-list__item {
    margin-bottom: 0 !important;
  }
  .el-upload-list__item-status-label {
    display: none !important;
  }
}

@include b(section-setter) {
  display: flex;
  @include e(item) {
    & + .v-section-setter__item {
      margin-left: 6px;
    }
  }
}

@include b(slider-setter) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .el-slider {
    --el-slider-height: 4px;
    --el-slider-button-size: 16px;
    margin: 0 10px;
  }
  @include e(close) {
    margin: 0 10px;
    cursor: pointer;
    &:hover {
      opacity: 0.7;
    }
  }
}
