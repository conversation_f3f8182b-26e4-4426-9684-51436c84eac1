import Logo from './logo/index.vue';
import EmptyWidget from './empty/index.vue';
import Switcher from './switcher/index.vue';
import Toolbar from './toolbar/index.vue';
import Actions from './actions/index.vue';
import Pages from './pages/index.vue';
import Blocks from './blocks/index.vue';
import Components from './components/index.vue';
import Outline from './outline/index.vue';
import History from './history/index.vue';
import Apis from './apis/index.vue';
import Deps from './deps/index.vue';
import Designer from './designer/index.vue';
import Scripts from './scripts/index.vue';
import Properties from './properties/index.vue';
import Events from './events/index.vue';
import Css from './css/index.vue';
import Directives from './directives/index.vue';
import Defined from './defined/index.vue';
import DataSources from './data-sources/index.vue';
import Style from './style/index.vue';
import Previewer from './previewer/index.vue';
import NodePath from './node-path/index.vue';
import Schema from './schema/index.vue';
import Raw from './raw/index.vue';
import Docs from './docs/index.vue';
import About from './about/index.vue';
import ProjectConfig from './project-config/index.vue';
import UniConfig from './uni-config/index.vue';
import Market from './market/index.vue';
import Templates from './templates/index.vue';
import UserAvatar from './user-avatar/index.vue';
import AI from './ai/index.vue';
export const widgets = {
  Logo,
  EmptyWidget,
  Switcher,
  Toolbar,
  Actions,
  Pages,
  Blocks,
  Components,
  Outline,
  History,
  Apis,
  Deps,
  Designer,
  Scripts,
  Properties,
  Events,
  Css,
  Directives,
  Defined,
  DataSources,
  Style,
  Previewer,
  NodePath,
  Schema,
  Raw,
  Docs,
  About,
  ProjectConfig,
  UniConfig,
  Market,
  Templates,
  UserAvatar,
  AI
};
