@use 'core' as *;

@include b(data-item) {
  @include e(wrapper) {
    font-size: 14px;
    color: var(--el-text-color-primary);

    font-weight: bold;
  }
  @include e(icon) {
    margin-right: 5px;
    box-sizing: content-box;
  }
  @include e(desc) {
    font-size: 12px;
    color: var(--el-text-color-regular);
  }

  @include e(actions) {
    padding: 10px 0;
    text-align: right;
  }

  @include e(section) {
    & + .x-data-item__section {
      margin-top: 5px;
    }
  }

  @include when(split) {
    & + .x-data-item.is-split {
      padding-top: 10px;
      margin-top: 10px;
      border-top: 1px solid var(--el-border-color-light);
    }
  }

  @include when(image-row) {
    .x-data-item__img {
      padding-right: 10px;
    }
  }
  @include when(image-column) {
    .x-data-item__img {
      padding-bottom: 10px;
    }
  }

  @include when(active) {
    background-color: var(--el-color-primary-light-9);
    border-radius: 4px;
  }

  @include when(hover) {
    &:hover {
      background-color: var(--el-color-primary-light-9);
      border-radius: 4px;
      opacity: 0.8;
    }

  }
}
