# 平台实现

VTJ 通过针对不同运行时环境的专用平台包提供多平台部署支持。每个平台实现都针对特定部署场景优化了低代码运行时，同时保持统一的开发体验。

## 平台架构概述

VTJ 平台生态系统由四个主要实现组成：

![](../svg/10/1.png)

VTJ 提供全面的多平台部署支持，涵盖 Web、移动和跨平台环境。平台层将低代码设计转换为针对每个目标运行时的优化应用程序。

## 平台矩阵

VTJ 通过专用适配器支持部署到多个平台类别：

| 平台包       | 目标环境        | 主要用例                          |
| ------------ | --------------- | --------------------------------- |
| @vtj/web     | 桌面 Web        | 使用 Element Plus 的标准 Web 应用 |
| @vtj/h5      | 移动 Web        | 针对移动设备优化的 H5 应用        |
| @vtj/pro     | 开发环境        | 可视化设计器和 IDE                |
| @vtj/pro-uni | 专业版 + 跨平台 | 带 UniApp 预览的专业 IDE          |
| @vtj/uni-app | 跨平台应用      | 原生应用和小程序                  |

### Web 平台

`@vtj/web` 平台面向具有完整 Element Plus 集成的桌面 Web 浏览器，为 VTJ 应用提供标准的 Web 运行时。

### 平台配置

![](../svg/10/2.png)

### 主要特性

- Element Plus UI 框架深度集成
- element-plus-admin 模板支持（v0.9.10 新增）
- SCSS 现代编译器支持（v0.9.0-alpha.0 新增）
- 移除 VXE 表格组件以优化包体积（v0.8.141）
- 自动更新功能（v0.8.161 新增）

## H5 移动平台

`@vtj/h5` 平台通过支持触摸的界面和移动设备特有功能，为移动 Web 环境优化 VTJ 应用。

### 移动优化特性

- 移动优先的响应式设计
- 触摸手势支持
- 移动 UI 框架集成（Vant）
- 针对移动网络优化的包体积
- H5 协议支持（v0.9.16 引入）

## 设计器平台

`@vtj/pro` 平台作为全面的 IDE 和设计环境，将可视化设计工具与多平台预览功能相结合。

### IDE 架构

![](../svg/10/3.png)

### AI 驱动功能

- 具有自然语言处理的 AI 助手（v0.12.0-alpha.0 新增）
- 图片转代码生成（AI 图生代码，v0.12.8 新增）
- Sketch 和 Figma 元数据识别（v0.12.32 新增）
- AI 对话取消支持（v0.12.10 新增）

### 设计时功能

- 模板管理系统（v0.9.0-alpha.0 新增）
- 路由位置定义（v0.9.0-alpha.0 新增）
- 访问控制支持（v0.8.166 新增）
- 应用增强配置（v0.12.20 新增）
- DevTools 模块集成（v0.8.131 新增）

## 跨平台应用

`@vtj/uni-app` 平台通过 DCloud uni-App 框架支持部署到多个移动平台和小程序。

### 多平台构建系统

UniApp 平台支持广泛的部署目标：

![](../svg/10/4.png)

### 平台依赖项

| 依赖包                   | 版本                   | 功能说明         |
| ------------------------ | ---------------------- | ---------------- |
| @dcloudio/uni-app        | 3.0.0-4050720250324001 | UniApp 框架核心  |
| @dcloudio/uni-components | 3.0.0-4050720250324001 | 标准组件库       |
| @dcloudio/uni-h5         | 3.0.0-4050720250324001 | H5 平台适配器    |
| @dcloudio/uni-mp-weixin  | 3.0.0-4050720250324001 | 微信小程序适配器 |
| @dcloudio/uni-ui         | ~1.5.3                 | UI 组件库        |
| @vtj/renderer            | workspace:~            | VTJ 渲染引擎     |
| @vtj/uni-app             | workspace:~            | VTJ UniApp 集成  |

### 构建命令

平台提供全面的构建命令矩阵：

```shell
# 开发命令
npm run dev:h5              # H5 开发模式
npm run dev:mp-weixin       # 微信小程序开发
npm run dev:app-android     # Android 开发
npm run dev:app-ios         # iOS 开发

# 生产构建
npm run build:h5            # H5 生产构建
npm run build:mp-weixin     # 微信小程序构建
npm run build:app           # 原生应用构建
```

### 平台检测元数据

平台包含用于自动检测的元数据：

```json
{
  "vtj": {
    "platform": "uniapp"
  }
}
```

## 平台选择与部署流程

多平台部署遵循统一的设计到部署流水线：

### 平台部署工作流

![](../svg/10/5.png)

### 环境配置

特定平台的环境配置：

| 环境变量                     | 功能说明      | 适用平台   |
| ---------------------------- | ------------- | ---------- |
| ENV_TYPE=local               | 开发模式      | 所有平台   |
| ENV_TYPE=sit                 | SIT 测试      | Web, H5    |
| ENV_TYPE=uat                 | UAT 测试      | Web, H5    |
| ENV_TYPE=live                | 生产环境      | 所有平台   |
| VITE_CJS_IGNORE_WARNING=true | 禁止 CJS 警告 | H5, UniApp |
| PREVIEW=true                 | 预览模式      | 所有平台   |

## 平台特定优化

### Web 平台优化

- **Element Plus 集成**：原生 Element Plus 组件映射
- **包体积优化**：移除 VXE 表格以减小体积（v0.8.141）
- **管理模板**：内置 element-plus-admin 集成（v0.9.9）
- **构建系统**：SCSS 现代编译器支持（v0.9.0-alpha.0）
- **性能优化**：实时部署的自动更新机制（v0.8.161）

### H5 移动优化

- **触摸界面**：移动优先的组件适配
- **包体积控制**：针对移动网络限制优化
- **移动框架**：Vant UI 移动组件集成
- **响应式设计**：自动视口和缩放调整

### 跨平台优化

- **统一代码库**：单一源码编译到 15+ 平台
- **平台 API**：各平台自动 API 映射
- **原生性能**：针对各目标的平台特定优化
- **小程序合规性**：自动平台合规检查

### 专业 IDE 优化

- **实时预览**：同时跨所有平台预览
- **AI 增强设计**：图像转代码和自然语言设计（v0.12.8）
- **多平台测试**：针对所有部署目标的集成测试
- **代码生成**：优化的平台特定代码输出

## 实施工作流程

使用不同平台实现时的典型工作流程：

![](../svg/10/6.png)

## 使用建议

选择项目平台实现时考虑以下因素：

1. 标准 Web 应用：使用 @vtj/web
2. 移动应用和小程序：使用 @vtj/uni-app
3. 开发和设计环境：使用 @vtj/pro

平台实现可根据项目需求单独或组合使用。@vtj/pro 环境可针对 Web 和 uni 平台进行预览和测试。

## 外部系统集成

平台实现提供多种外部系统集成方式：

- 通过 @vtj/pro 中的访问控制功能进行认证授权
- 自定义数据源和 API 集成
- Web 应用的 Element Plus Admin 集成
- 移动应用的 UniApp 生态系统集成

通过这些集成点，VTJ 应用可连接到各种后端系统和数据源，同时保持一致的视觉设计和开发体验。
