@use '../core' as *;

@include b(item) {
  padding: 3px 5px;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 4px;
  min-height: 28px;
  border: 1px solid transparent;
  cursor: pointer;
  &:hover {
    border: 1px dashed var(--el-color-primary-light-7) !important;
    background-color: var(--el-color-primary-light-9) !important;
  }

  .el-switch--small {
    height: 16px;
  }
  .x-action__inner:hover {
    background-color: var(--el-color-primary-light-7);
  }

  @include e(switch) {
    margin-left: 5px;
  }

  @include e(index) {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    display: inline-block;
    width: 30px;
    flex-shrink: 0;
  }

  @include e(tag) {
    margin-right: 10px;
    zoom: 0.7;
  }

  @include e(tags) {
    position: absolute;
    right: 30px;
    font-size: 12px;
    > span {
      margin: 0 2px;
      zoom: 0.9;
      color: var(--el-color-info);
      opacity: 0.8;
    }
  }

  @include e(title-wrapper) {
    overflow: hidden;
    text-overflow: ellipsis;
    width: 1px;
    flex-grow: 1;
  }

  @include when(nowrap) {
    @include e(title-wrapper) {
      white-space: nowrap;
    }
  }

  @include e(title) {
    flex-grow: 1;
    > .x-icon {
      margin-right: 3px;
    }
  }
  @include e(subtitle) {
    margin-left: 5px;
    font-style: italic;
    opacity: 0.5;
  }

  @include when(small) {
    min-height: 24px;
    height: 24px;
  }

  @include when(active) {
    background-color: var(--el-color-primary-light-9) !important;
    color: var(--el-color-primary) !important;
  }

  @include when(background) {
    background-color: var(--el-fill-color-lighter);
  }
  @include when(border) {
    border: 1px dashed var(--el-border-color-lighter);
  }

  @include when(hover) {
    .v-item__actions {
      display: none;
    }
    &:hover {
      .v-item__actions {
        display: block;
      }
    }
  }
}
