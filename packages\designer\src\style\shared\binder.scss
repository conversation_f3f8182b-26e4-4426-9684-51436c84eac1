@use '../core' as *;

@include b(binder) {
  @include e(tabs) {
    .x-panel__header {
      background-color: var(--el-bg-color) !important;
      padding: 0 !important;
    }
    .el-tabs__item {
      border-top: 1px solid var(--el-border-color-light) !important;
    }
  }

  @include e(tab-content) {
    background-color: var(--el-bg-color);
    border-radius: 4px;
    overflow: auto;

    .el-divider__text {
      font-size: 12px;
      color: var(--el-text-color-regular);
    }
    .el-divider--horizontal {
      margin: 14px 0;
    }
  }

  @include e(form) {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    .v-binder__editor {
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      .el-form-item__content {
        flex-grow: 1;
        position: relative;
      }
      .x-field__editor_wrap {
        position: absolute;
        inset: 0;
      }
    }
  }
}

@include b(context-viewer) {
  .el-tree {
    --el-tree-node-hover-bg-color: transparent;
    .v-item {
      margin: 0;
    }
    .v-item__subtitle {
      font-size: 12px;
      &.Function,
      &.AsyncFunction {
        color: var(--el-color-warning);
      }
      &.Object,
      &.Array {
        color: var(--el-color-success);
      }
      &.Module {
        color: var(--el-color-primary);
      }
    }
  }
  .el-tree-node__content {
    height: 28px;
  }
}
