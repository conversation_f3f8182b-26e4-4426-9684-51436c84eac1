@use 'core' as *;

@include b(tabs) {
  --el-tabs-header-height: 30px !important;
  &.el-tabs--left .el-tabs__nav-wrap.is-left:after,
  &.el-tabs--left .el-tabs__nav-wrap.is-right:after,
  &.el-tabs--right .el-tabs__nav-wrap.is-left:after,
  &.el-tabs--right .el-tabs__nav-wrap.is-right:after {
    width: 1px;
  }

  @include when(no-border) {
    .el-tabs__nav-wrap::after {
      background-color: transparent;
    }
  }

  @include when(align-center) {
    .el-tabs__nav-scroll {
      display: flex;
      justify-content: center;
    }
  }

  @include when(align-right) {
    .el-tabs__nav-scroll {
      display: flex;
      justify-content: flex-end;
    }
  }

  @include when(fit) {
    width: 100%;
    height: 100%;
  }
  .el-tabs__header {
    margin-bottom: 0;
  }

  .el-tabs__item {
    padding: 0 15px;
    display: flex;
    justify-content: space-between;
    &.is-left,
    &.is-right {
      padding-top: 3px;
      padding-bottom: 3px;
    }
  }
  &.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
    background: var(--el-bg-color);
  }

  &.el-tabs--top.el-tabs--card > .el-tabs__header .el-tabs__item:nth-child(2) {
    padding-left: 15px;
  }

  &.el-tabs--top.el-tabs--card > .el-tabs__header .el-tabs__item:last-child {
    padding-right: 15px;
  }

  @include e(icon) {
    margin-right: 5px;
  }

  @include e(label-inner) {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-grow: 1;
  }
  @include e(actions) {
    padding-left: 10px;
  }
}

.x-panel__body > .x-tabs {
  position: relative;
  top: -5px;
}
