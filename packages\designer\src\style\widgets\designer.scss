@use '../core' as *;

@include b(designer) {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  position: relative;
  overflow: auto;

  iframe {
    width: 100%;
    height: 100%;
    border: none;
  }

  .el-empty {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: var(--el-bg-color);
  }

  @include e(hover) {
    border: 2px dashed var(--el-color-warning-dark-2);
    background-color: rgba(230, 162, 60, 0.1);
    position: absolute;
    left: 0;
    top: 0;
    pointer-events: none;
    border-radius: 4px;

    > span {
      display: inline-block;
      color: var(--el-color-warning-dark-2);
      position: absolute;
      background-color: var(--el-color-warning-light-7);
      border-radius: 4px;
      font-size: 12px;
      padding: 4px 8px;
      white-space: nowrap;
      box-shadow: var(--el-box-shadow-lighter);
      z-index: 10;
      > i {
        color: var(--el-text-color-secondary);
      }
      &.is-inner {
        left: 4px;
        top: 4px;
      }

      &.is-left-top {
        left: -2px;
        top: -4px;
        transform: translateY(-100%);
      }

      &.is-left-bottom {
        left: -2px;
        bottom: -4px;
        transform: translateY(100%);
      }
      &.is-right-top {
        right: 0;
        top: -4px;
        transform: translateY(-100%);
      }
      &.is-right-bottom {
        right: 0;
        bottom: -4px;
        transform: translateY(100%);
      }
    }
  }

  @include e(dropping) {
    border: 4px solid var(--el-color-success-dark-2);
    border-radius: 4px;
    background-color: rgba(82, 155, 46, 0.1);
    position: absolute;
    pointer-events: none;
    z-index: 20;
    &.is-inner {
      border: 2px solid var(--el-color-success-dark-2);
    }
  }

  @include e(selected) {
    border: 2px solid var(--el-color-primary-dark-2);
    border-radius: 4px;
    background-color: rgba(64, 158, 255, 0.1);
    position: absolute;
    left: 0;
    top: 0;
    pointer-events: none;
    z-index: 10;
  }
  @include e(placeholder) {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    font-size: 14px;
    color: var(--el-text-color-placeholder);
    opacity: 0.6;
    pointer-events: none;
  }

  @include e(outline-lines) {
    pointer-events: none;
    > div {
      position: absolute;
      pointer-events: none;
      border: 1px dashed var(--el-border-color-light);
    }
  }
}

@include b(actions) {
  position: absolute;
  pointer-events: initial;
  font-size: 12px;
  white-space: nowrap;
  display: flex;
  > .x-action,
  > .x-action-bar {
    background-color: var(--el-color-primary-dark-2);
    border-radius: 4px;
    padding: 2px 5px;
    box-shadow: var(--el-box-shadow-lighter);

    .x-action__inner {
      color: var(--el-color-primary-light-9);
    }
  }
  > .x-action-bar {
    margin-left: 5px;
  }

  .x-action:hover {
    background-color: var(--el-color-primary);
    border-radius: 4px;
  }
  &.is-inner {
    right: 2px;
    top: 2px;
  }

  &.is-left-top {
    left: -2px;
    top: -4px;
    transform: translateY(-100%);
  }

  &.is-left-bottom {
    left: 0;
    bottom: -4px;
    transform: translateY(100%);
  }
  &.is-right-top {
    right: 0;
    top: -4px;
    transform: translateY(-100%);
  }
  &.is-right-bottom {
    right: -2px;
    bottom: -4px;
    transform: translateY(100%);
  }
}
