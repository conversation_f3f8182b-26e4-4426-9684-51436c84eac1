import StringSetter from './string.vue';
import BooleanSetter from './boolean.vue';
import ExpressionSetter from './expression.vue';
import SelectSetter from './select.vue';
import NumberSetter from './number.vue';
import ColorSetter from './color.vue';
import IconSetter from './icon.vue';
import JsonSetter from './json.vue';
import FunctionSetter from './function.vue';
import RadioSetter from './radio.vue';
import TagSetter from './tag.vue';
import SizeSetter from './size.vue';
import ImageSetter from './image.vue';
import SectionSetter from './section.vue';
import SliderSetter from './slider.vue';
import FileSetter from './file.vue';
import VanIconSetter from './vanIcon/index.vue';
import CssSetter from './css.vue';

export const setters = {
  StringSetter,
  BooleanSetter,
  ExpressionSetter,
  SelectSetter,
  NumberSetter,
  ColorSetter,
  IconSetter,
  JsonSetter,
  FunctionSetter,
  RadioSetter,
  TagSetter,
  SizeSetter,
  ImageSetter,
  SectionSetter,
  SliderSetter,
  FileSetter,
  VanIconSetter,
  CssSetter
};
