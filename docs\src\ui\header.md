# XHeader 标题头

## 示例

### 基础用法

:::preview
demo-preview=../examples/ui/header/header-1.vue
:::


## API

### 属性

| 属性名   | 说明     | 类型               | 默认值    |
| -------- | -------- | ------------------ | --------- |
| size     | 尺寸     | `string`           | `default` |
| content  | 内容     | `string`           | ''        |
| subtitle | 二级标题 | `string`           | -         |
| icon     | 图标     | `string \| object` | -         |
| border   | 边框     | `boolean`          | -         |
| more     | -        | `boolean`          | -         |



### 事件

| 名称      | 说明         | 参数 |
| --------- | ------------ | ---- |
| click     | 点击事件     | -    |
| clickIcon | 点击图标事件 | -    |
