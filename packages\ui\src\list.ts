import { type DefineComponent } from 'vue';
import {
  XIcon,
  XMenu,
  XAction,
  XActionBar,
  XContainer,
  XHeader,
  XPanel,
  XDialog,
  XMask,
  XField,
  XForm,
  XDialogForm,
  XTabs,
  XDataItem,
  XList,
  XStartup,
  XTest,
  XInputUnit,
  XCaptcha,
  XQrCode,
  XVerify,
  XAttachment,
  XQueryForm,
  XGrid,
  XPicker,
  XImportButton
} from './components';

export const components = [
  XIcon,
  XMenu,
  XAction,
  XActionBar,
  XContainer,
  XHeader,
  XPanel,
  XDialog,
  XMask,
  XField,
  XForm,
  XDialogForm,
  XTabs,
  XDataItem,
  XList,
  XStartup,
  XTest,
  XInputUnit,
  XCaptcha,
  XQrCode,
  XVerify,
  XAttachment,
  XQueryForm,
  XGrid,
  XPicker,
  XImportButton
] as DefineComponent<any, any, any, any>[];
