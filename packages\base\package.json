{"name": "@vtj/base", "private": false, "version": "0.12.2", "type": "module", "keywords": ["低代码引擎", "LowCode Engine", "Vue3低代码", "低代码渲染器", "低代码设计器", "代码生成器", "代码可视化"], "description": "VTJ 是一款基于 Vue3 + Typescript 的低代码页面可视化设计器。内置低代码引擎、渲染器和代码生成器，面向前端开发者，开箱即用。 无缝嵌入本地开发工程，不改变前端开发流程和编码习惯。", "repository": {"type": "git", "url": "https://gitee.com/newgateway/vtj.git"}, "homepage": "https://gitee.com/newgateway/vtj", "author": "chenhuachun", "license": "MIT", "scripts": {"build": "unbuild && npm run dts", "test": "vitest run", "vitest": "vitest", "coverage": "vitest run --coverage", "dts": "tsc src/index.ts --target es2020 --moduleResolution Node --outDir temp --declarationDir types --declaration --esModuleInterop"}, "dependencies": {"@types/lodash-es": "~4.17.12", "@types/numeral": "~2.0.5", "reflect-metadata": "~0.2.1"}, "devDependencies": {"@types/crypto-js": "~4.2.2", "crypto-js": "~4.2.0", "dayjs": "~1.11.10", "jsencrypt": "~3.3.2", "lodash-es": "~4.17.21", "lz-string": "~1.5.0", "mitt": "~3.0.1", "numeral": "~2.0.6", "path-to-regexp": "~6.2.1", "unbuild": "~2.0.0"}, "exports": {".": {"types": "./types/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./types/index.d.ts", "files": ["dist", "types"], "publishConfig": {"access": "public"}, "gitHead": "d03843144f07c2d98c1e0c72c8c6eb1117c01722"}