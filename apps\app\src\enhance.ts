import type { App } from 'vue';
import type { Provider } from '@vtj/renderer';
import { createPinia, defineStore } from 'pinia';
import { authService } from './services/auth';
import axios from 'axios';
import './style/global.scss';
const pinia = createPinia();
const useCounterStore = defineStore('counter', {
  state: () => ({
    count: 0
  }),
  getters: {
    doubleCount: (state) => state.count * 2
  }
});

export default function (app: App, provider: Provider) {
  app.use(pinia);
  app.provide('counter', useCounterStore());

  // 配置axios基础URL
  if (process.env.NODE_ENV === 'production') {
    axios.defaults.baseURL = 'http://192.168.80.79:8000';
  }

  // 初始化认证服务
  authService.init();

  // 将认证服务注入到Vue应用中，以便组件可以访问
  app.provide('authService', authService);
  app.config.globalProperties.$authService = authService;

  // 配置请求拦截器
  provider.adapter.request.useRequest((config) => {
    // 注入认证头
    config.headers.Token = '123456';
    return config;
  });

  // 配置响应拦截器
  provider.adapter.request.useResponse((res) => {
    // 处理认证失败
    if (res.data && res.data.code === '1001') {
      // 认证失败，跳转到登录页
      authService.logout();
      // 注意：VTJ的响应拦截器不支持自动重试，需要在业务层处理
    }
    return res;
  });
}
