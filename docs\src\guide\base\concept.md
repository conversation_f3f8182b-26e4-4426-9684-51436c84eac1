# 相关概念

为了更清晰地描述低代码平台的工作机制，需要理解以下核心概念：

- **协议**
  —— 指低代码开发平台中组件和页面的描述规范，定义了组件的属性、事件、插槽等元数据格式。

- **DSL (Domain Specific Language)**
  —— 领域特定语言，基于协议生成的JSON结构数据，用于描述页面布局、组件关系及配置。示例结构：

  ```json
  {
    "component": "Page",
    "props": {...},
    "children": [...]
  }
  ```

- **页面**
  —— 通过设计器创建的Vue单文件组件（.vue），具有独立路由，发布后可通过 `/page/[页面ID]` 路径访问。

- **区块**
  —— 可复用的Vue单文件组件，不含路由配置，可被页面或其他区块引用组合。

- **物料**
  —— 符合低代码协议的Vue组件，包含组件元数据描述，可在设计器中拖拽使用。

## 工作数据流

![](../../assets/newpearl/13.png)

设计器通过可视化操作将低代码物料转换为DSL，主要流程：

1. 从物料库拖拽组件到画布
2. 在属性面板配置组件参数
3. 生成描述页面结构的DSL
4. 通过渲染引擎将DSL转换为可运行代码

## 入口链接

启动低代码开发环境后，在页面右下角会出现编辑图标，点击该图标可进入设计器并打开当前页面的设计模式。

![](../../assets/newpearl/3.png)

## 功能分区

低代码设计器采用模块化分区架构，每个区域内置相应的功能组件（Widget）：

![](../../assets/newpearl/5.png)

- **品牌区：** 显示平台Logo、当前项目名称及正在编辑的组件。点击项目名称可返回源码预览模式。
- **工具区：** 提供模拟器视图切换（PC/移动）、操作历史导航（撤销/重做）。
- **操作区：** 包含页面预览、刷新、设置及发布功能。
- **应用区：** 管理页面、区块、物料库、组件树结构、编辑历史、API接口、数据源、依赖和项目配置。
- **工作区：** 核心设计区域，支持设计视图、DSL代码视图、源码视图、帮助文档及物料市场。
- **设置区：**
  - 页面级设置：状态数据、计算属性、方法、生命周期、监听器、CSS样式、数据源、组件定义
  - 组件级设置：属性配置、样式调整、事件绑定、指令添加
- **状态区：** 显示当前选中节点的详细信息及错误报告。
