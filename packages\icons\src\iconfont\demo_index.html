<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=3351036" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe602;</span>
                <div class="name">历史对话</div>
                <div class="code-name">&amp;#xe602;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe7ae;</span>
                <div class="name">新对话</div>
                <div class="code-name">&amp;#xe7ae;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe601;</span>
                <div class="name">ai</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe6af;</span>
                <div class="name">uniapp</div>
                <div class="code-name">&amp;#xe6af;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe7e8;</span>
                <div class="name">窗体-窗口化-细</div>
                <div class="code-name">&amp;#xe7e8;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe7e9;</span>
                <div class="name">窗体-窗体化-细</div>
                <div class="code-name">&amp;#xe7e9;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe7ea;</span>
                <div class="name">窗体-关闭-细</div>
                <div class="code-name">&amp;#xe7ea;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe7eb;</span>
                <div class="name">窗体-窗口最大化-细</div>
                <div class="code-name">&amp;#xe7eb;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe7ec;</span>
                <div class="name">窗体-折叠</div>
                <div class="code-name">&amp;#xe7ec;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe7ed;</span>
                <div class="name">窗体-展开</div>
                <div class="code-name">&amp;#xe7ed;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe608;</span>
                <div class="name">保存</div>
                <div class="code-name">&amp;#xe608;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe609;</span>
                <div class="name">查看文件</div>
                <div class="code-name">&amp;#xe609;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe60f;</span>
                <div class="name">编辑</div>
                <div class="code-name">&amp;#xe60f;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe610;</span>
                <div class="name">冲销</div>
                <div class="code-name">&amp;#xe610;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe613;</span>
                <div class="name">查询</div>
                <div class="code-name">&amp;#xe613;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe614;</span>
                <div class="name">导出</div>
                <div class="code-name">&amp;#xe614;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe615;</span>
                <div class="name">导入</div>
                <div class="code-name">&amp;#xe615;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe616;</span>
                <div class="name">清单</div>
                <div class="code-name">&amp;#xe616;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe617;</span>
                <div class="name">打印</div>
                <div class="code-name">&amp;#xe617;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe619;</span>
                <div class="name">取消</div>
                <div class="code-name">&amp;#xe619;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe61a;</span>
                <div class="name">确认</div>
                <div class="code-name">&amp;#xe61a;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe61c;</span>
                <div class="name">全消</div>
                <div class="code-name">&amp;#xe61c;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe61e;</span>
                <div class="name">返回所有数据</div>
                <div class="code-name">&amp;#xe61e;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe61f;</span>
                <div class="name">返回数据</div>
                <div class="code-name">&amp;#xe61f;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe621;</span>
                <div class="name">删单</div>
                <div class="code-name">&amp;#xe621;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe624;</span>
                <div class="name">删行</div>
                <div class="code-name">&amp;#xe624;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe625;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xe625;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe626;</span>
                <div class="name">退出</div>
                <div class="code-name">&amp;#xe626;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe627;</span>
                <div class="name">刷新</div>
                <div class="code-name">&amp;#xe627;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe62b;</span>
                <div class="name">新单</div>
                <div class="code-name">&amp;#xe62b;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe62c;</span>
                <div class="name">选择</div>
                <div class="code-name">&amp;#xe62c;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe62e;</span>
                <div class="name">增行</div>
                <div class="code-name">&amp;#xe62e;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe62f;</span>
                <div class="name">向下展开</div>
                <div class="code-name">&amp;#xe62f;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe630;</span>
                <div class="name">关闭</div>
                <div class="code-name">&amp;#xe630;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe632;</span>
                <div class="name">提交</div>
                <div class="code-name">&amp;#xe632;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe6ef;</span>
                <div class="name">管理依赖</div>
                <div class="code-name">&amp;#xe6ef;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe650;</span>
                <div class="name">返回</div>
                <div class="code-name">&amp;#xe650;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe70e;</span>
                <div class="name">首页</div>
                <div class="code-name">&amp;#xe70e;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe661;</span>
                <div class="name">API管理</div>
                <div class="code-name">&amp;#xe661;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe611;</span>
                <div class="name">导出</div>
                <div class="code-name">&amp;#xe611;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe612;</span>
                <div class="name">导入</div>
                <div class="code-name">&amp;#xe612;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe65f;</span>
                <div class="name">向右箭头</div>
                <div class="code-name">&amp;#xe65f;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe660;</span>
                <div class="name">向左箭头</div>
                <div class="code-name">&amp;#xe660;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe60e;</span>
                <div class="name">正确</div>
                <div class="code-name">&amp;#xe60e;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe62a;</span>
                <div class="name">切换</div>
                <div class="code-name">&amp;#xe62a;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe629;</span>
                <div class="name">复制</div>
                <div class="code-name">&amp;#xe629;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe676;</span>
                <div class="name">lock</div>
                <div class="code-name">&amp;#xe676;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe683;</span>
                <div class="name">unlock</div>
                <div class="code-name">&amp;#xe683;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe60d;</span>
                <div class="name">层级</div>
                <div class="code-name">&amp;#xe60d;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe669;</span>
                <div class="name">code</div>
                <div class="code-name">&amp;#xe669;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe605;</span>
                <div class="name">团队</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xeb88;</span>
                <div class="name">icon_发布</div>
                <div class="code-name">&amp;#xeb88;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe655;</span>
                <div class="name">预览</div>
                <div class="code-name">&amp;#xe655;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe618;</span>
                <div class="name">保存</div>
                <div class="code-name">&amp;#xe618;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe65e;</span>
                <div class="name">分类-电脑平板</div>
                <div class="code-name">&amp;#xe65e;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe921;</span>
                <div class="name">平板电脑,移动设备</div>
                <div class="code-name">&amp;#xe921;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe684;</span>
                <div class="name">23C平板</div>
                <div class="code-name">&amp;#xe684;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe60a;</span>
                <div class="name">refresh-right</div>
                <div class="code-name">&amp;#xe60a;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe60b;</span>
                <div class="name">refresh</div>
                <div class="code-name">&amp;#xe60b;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe60c;</span>
                <div class="name">refresh-left</div>
                <div class="code-name">&amp;#xe60c;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe65d;</span>
                <div class="name">分类</div>
                <div class="code-name">&amp;#xe65d;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe66a;</span>
                <div class="name">项目</div>
                <div class="code-name">&amp;#xe66a;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe67c;</span>
                <div class="name">通知</div>
                <div class="code-name">&amp;#xe67c;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe67d;</span>
                <div class="name">收藏</div>
                <div class="code-name">&amp;#xe67d;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe8e8;</span>
                <div class="name">bug</div>
                <div class="code-name">&amp;#xe8e8;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xeabe;</span>
                <div class="name">24gl-fileText</div>
                <div class="code-name">&amp;#xeabe;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xeabf;</span>
                <div class="name">24gl-folderOpen</div>
                <div class="code-name">&amp;#xeabf;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe628;</span>
                <div class="name">上传</div>
                <div class="code-name">&amp;#xe628;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe68c;</span>
                <div class="name">操作-上传-download</div>
                <div class="code-name">&amp;#xe68c;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe682;</span>
                <div class="name">user</div>
                <div class="code-name">&amp;#xe682;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe8b7;</span>
                <div class="name">205设置-线性</div>
                <div class="code-name">&amp;#xe8b7;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xeb08;</span>
                <div class="name">箭头_向右</div>
                <div class="code-name">&amp;#xeb08;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xeb09;</span>
                <div class="name">箭头_向左</div>
                <div class="code-name">&amp;#xeb09;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xeb0a;</span>
                <div class="name">箭头_向下</div>
                <div class="code-name">&amp;#xeb0a;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xeb0b;</span>
                <div class="name">箭头_向上</div>
                <div class="code-name">&amp;#xeb0b;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe620;</span>
                <div class="name">分享</div>
                <div class="code-name">&amp;#xe620;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe93a;</span>
                <div class="name">数据库,数据</div>
                <div class="code-name">&amp;#xe93a;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe677;</span>
                <div class="name">模板</div>
                <div class="code-name">&amp;#xe677;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xea68;</span>
                <div class="name">24gl-fullScreenEnter</div>
                <div class="code-name">&amp;#xea68;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xea69;</span>
                <div class="name">24gl-fullScreenEnter3</div>
                <div class="code-name">&amp;#xea69;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe604;</span>
                <div class="name">编辑</div>
                <div class="code-name">&amp;#xe604;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe61b;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xe61b;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe6a0;</span>
                <div class="name">符号-JSX</div>
                <div class="code-name">&amp;#xe6a0;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe658;</span>
                <div class="name">配比数据库</div>
                <div class="code-name">&amp;#xe658;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xed1b;</span>
                <div class="name">信息空心</div>
                <div class="code-name">&amp;#xed1b;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xed21;</span>
                <div class="name">加_色块</div>
                <div class="code-name">&amp;#xed21;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xed22;</span>
                <div class="name">减_色块</div>
                <div class="code-name">&amp;#xed22;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe8ac;</span>
                <div class="name">帮助</div>
                <div class="code-name">&amp;#xe8ac;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe643;</span>
                <div class="name">代码</div>
                <div class="code-name">&amp;#xe643;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe623;</span>
                <div class="name">大纲管理_默认</div>
                <div class="code-name">&amp;#xe623;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe7e2;</span>
                <div class="name">明文显示</div>
                <div class="code-name">&amp;#xe7e2;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe7e7;</span>
                <div class="name">秘文显示</div>
                <div class="code-name">&amp;#xe7e7;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe61d;</span>
                <div class="name">文档-默认</div>
                <div class="code-name">&amp;#xe61d;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xea10;</span>
                <div class="name">历史记录</div>
                <div class="code-name">&amp;#xea10;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe9b9;</span>
                <div class="name">解除固定,图钉</div>
                <div class="code-name">&amp;#xe9b9;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe9ba;</span>
                <div class="name">解除固定,图钉</div>
                <div class="code-name">&amp;#xe9ba;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe622;</span>
                <div class="name">搜索</div>
                <div class="code-name">&amp;#xe622;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe64b;</span>
                <div class="name">more</div>
                <div class="code-name">&amp;#xe64b;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe600;</span>
                <div class="name">3.1关闭</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe652;</span>
                <div class="name">组件</div>
                <div class="code-name">&amp;#xe652;</div>
              </li>
          
            <li class="dib">
              <span class="icon VtjIconfont">&#xe62d;</span>
                <div class="name">元数据-组件库</div>
                <div class="code-name">&amp;#xe62d;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'VtjIconfont';
  src: url('iconfont.woff2?t=1740793194712') format('woff2'),
       url('iconfont.woff?t=1740793194712') format('woff'),
       url('iconfont.ttf?t=1740793194712') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.VtjIconfont {
  font-family: "VtjIconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="VtjIconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"VtjIconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-chat-record"></span>
            <div class="name">
              历史对话
            </div>
            <div class="code-name">.vtj-icon-chat-record
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-new-chat"></span>
            <div class="name">
              新对话
            </div>
            <div class="code-name">.vtj-icon-new-chat
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-ai"></span>
            <div class="name">
              ai
            </div>
            <div class="code-name">.vtj-icon-ai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-uniapp"></span>
            <div class="name">
              uniapp
            </div>
            <div class="code-name">.vtj-icon-uniapp
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-window-max"></span>
            <div class="name">
              窗体-窗口化-细
            </div>
            <div class="code-name">.vtj-icon-window-max
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-window-min"></span>
            <div class="name">
              窗体-窗体化-细
            </div>
            <div class="code-name">.vtj-icon-window-min
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-window-close"></span>
            <div class="name">
              窗体-关闭-细
            </div>
            <div class="code-name">.vtj-icon-window-close
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-window-normal"></span>
            <div class="name">
              窗体-窗口最大化-细
            </div>
            <div class="code-name">.vtj-icon-window-normal
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-window-down"></span>
            <div class="name">
              窗体-折叠
            </div>
            <div class="code-name">.vtj-icon-window-down
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-window-up"></span>
            <div class="name">
              窗体-展开
            </div>
            <div class="code-name">.vtj-icon-window-up
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-np-save"></span>
            <div class="name">
              保存
            </div>
            <div class="code-name">.vtj-icon-np-save
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-np-file"></span>
            <div class="name">
              查看文件
            </div>
            <div class="code-name">.vtj-icon-np-file
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-np-edit"></span>
            <div class="name">
              编辑
            </div>
            <div class="code-name">.vtj-icon-np-edit
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-np-share"></span>
            <div class="name">
              冲销
            </div>
            <div class="code-name">.vtj-icon-np-share
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-np-search"></span>
            <div class="name">
              查询
            </div>
            <div class="code-name">.vtj-icon-np-search
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-np-export"></span>
            <div class="name">
              导出
            </div>
            <div class="code-name">.vtj-icon-np-export
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-np-import"></span>
            <div class="name">
              导入
            </div>
            <div class="code-name">.vtj-icon-np-import
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-np-list"></span>
            <div class="name">
              清单
            </div>
            <div class="code-name">.vtj-icon-np-list
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-np-print"></span>
            <div class="name">
              打印
            </div>
            <div class="code-name">.vtj-icon-np-print
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-np-cancel"></span>
            <div class="name">
              取消
            </div>
            <div class="code-name">.vtj-icon-np-cancel
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-np-confirm"></span>
            <div class="name">
              确认
            </div>
            <div class="code-name">.vtj-icon-np-confirm
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-np-reset"></span>
            <div class="name">
              全消
            </div>
            <div class="code-name">.vtj-icon-np-reset
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-np-return-all"></span>
            <div class="name">
              返回所有数据
            </div>
            <div class="code-name">.vtj-icon-np-return-all
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-np-return"></span>
            <div class="name">
              返回数据
            </div>
            <div class="code-name">.vtj-icon-np-return
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-np-remove"></span>
            <div class="name">
              删单
            </div>
            <div class="code-name">.vtj-icon-np-remove
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-np-remove-row"></span>
            <div class="name">
              删行
            </div>
            <div class="code-name">.vtj-icon-np-remove-row
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-np-delete"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.vtj-icon-np-delete
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-np-exit"></span>
            <div class="name">
              退出
            </div>
            <div class="code-name">.vtj-icon-np-exit
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-np-refresh"></span>
            <div class="name">
              刷新
            </div>
            <div class="code-name">.vtj-icon-np-refresh
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-np-add"></span>
            <div class="name">
              新单
            </div>
            <div class="code-name">.vtj-icon-np-add
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-np-select"></span>
            <div class="name">
              选择
            </div>
            <div class="code-name">.vtj-icon-np-select
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-np-add-row"></span>
            <div class="name">
              增行
            </div>
            <div class="code-name">.vtj-icon-np-add-row
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-np-extend"></span>
            <div class="name">
              向下展开
            </div>
            <div class="code-name">.vtj-icon-np-extend
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-np-close"></span>
            <div class="name">
              关闭
            </div>
            <div class="code-name">.vtj-icon-np-close
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-np-submit"></span>
            <div class="name">
              提交
            </div>
            <div class="code-name">.vtj-icon-np-submit
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-deps"></span>
            <div class="name">
              管理依赖
            </div>
            <div class="code-name">.vtj-icon-deps
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-back"></span>
            <div class="name">
              返回
            </div>
            <div class="code-name">.vtj-icon-back
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-home"></span>
            <div class="name">
              首页
            </div>
            <div class="code-name">.vtj-icon-home
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-api"></span>
            <div class="name">
              API管理
            </div>
            <div class="code-name">.vtj-icon-api
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-export"></span>
            <div class="name">
              导出
            </div>
            <div class="code-name">.vtj-icon-export
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-import"></span>
            <div class="name">
              导入
            </div>
            <div class="code-name">.vtj-icon-import
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-greater"></span>
            <div class="name">
              向右箭头
            </div>
            <div class="code-name">.vtj-icon-greater
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-smaller"></span>
            <div class="name">
              向左箭头
            </div>
            <div class="code-name">.vtj-icon-smaller
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-check"></span>
            <div class="name">
              正确
            </div>
            <div class="code-name">.vtj-icon-check
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-switch"></span>
            <div class="name">
              切换
            </div>
            <div class="code-name">.vtj-icon-switch
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-copy"></span>
            <div class="name">
              复制
            </div>
            <div class="code-name">.vtj-icon-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-lock"></span>
            <div class="name">
              lock
            </div>
            <div class="code-name">.vtj-icon-lock
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-unlock"></span>
            <div class="name">
              unlock
            </div>
            <div class="code-name">.vtj-icon-unlock
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-layers"></span>
            <div class="name">
              层级
            </div>
            <div class="code-name">.vtj-icon-layers
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-console"></span>
            <div class="name">
              code
            </div>
            <div class="code-name">.vtj-icon-console
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-team"></span>
            <div class="name">
              团队
            </div>
            <div class="code-name">.vtj-icon-team
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-publish"></span>
            <div class="name">
              icon_发布
            </div>
            <div class="code-name">.vtj-icon-publish
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-preview"></span>
            <div class="name">
              预览
            </div>
            <div class="code-name">.vtj-icon-preview
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-save"></span>
            <div class="name">
              保存
            </div>
            <div class="code-name">.vtj-icon-save
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-pc"></span>
            <div class="name">
              分类-电脑平板
            </div>
            <div class="code-name">.vtj-icon-pc
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-phone"></span>
            <div class="name">
              平板电脑,移动设备
            </div>
            <div class="code-name">.vtj-icon-phone
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-pad"></span>
            <div class="name">
              23C平板
            </div>
            <div class="code-name">.vtj-icon-pad
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-redo"></span>
            <div class="name">
              refresh-right
            </div>
            <div class="code-name">.vtj-icon-redo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-refresh"></span>
            <div class="name">
              refresh
            </div>
            <div class="code-name">.vtj-icon-refresh
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-undo"></span>
            <div class="name">
              refresh-left
            </div>
            <div class="code-name">.vtj-icon-undo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-category"></span>
            <div class="name">
              分类
            </div>
            <div class="code-name">.vtj-icon-category
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-project"></span>
            <div class="name">
              项目
            </div>
            <div class="code-name">.vtj-icon-project
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-notice"></span>
            <div class="name">
              通知
            </div>
            <div class="code-name">.vtj-icon-notice
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-fav"></span>
            <div class="name">
              收藏
            </div>
            <div class="code-name">.vtj-icon-fav
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-bug"></span>
            <div class="name">
              bug
            </div>
            <div class="code-name">.vtj-icon-bug
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-file"></span>
            <div class="name">
              24gl-fileText
            </div>
            <div class="code-name">.vtj-icon-file
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-folder"></span>
            <div class="name">
              24gl-folderOpen
            </div>
            <div class="code-name">.vtj-icon-folder
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-upload"></span>
            <div class="name">
              上传
            </div>
            <div class="code-name">.vtj-icon-upload
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-download"></span>
            <div class="name">
              操作-上传-download
            </div>
            <div class="code-name">.vtj-icon-download
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-user"></span>
            <div class="name">
              user
            </div>
            <div class="code-name">.vtj-icon-user
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-setting"></span>
            <div class="name">
              205设置-线性
            </div>
            <div class="code-name">.vtj-icon-setting
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-arrow-right"></span>
            <div class="name">
              箭头_向右
            </div>
            <div class="code-name">.vtj-icon-arrow-right
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-arrow-left"></span>
            <div class="name">
              箭头_向左
            </div>
            <div class="code-name">.vtj-icon-arrow-left
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-arrow-down"></span>
            <div class="name">
              箭头_向下
            </div>
            <div class="code-name">.vtj-icon-arrow-down
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-arrow-up"></span>
            <div class="name">
              箭头_向上
            </div>
            <div class="code-name">.vtj-icon-arrow-up
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-share"></span>
            <div class="name">
              分享
            </div>
            <div class="code-name">.vtj-icon-share
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-data"></span>
            <div class="name">
              数据库,数据
            </div>
            <div class="code-name">.vtj-icon-data
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-template"></span>
            <div class="name">
              模板
            </div>
            <div class="code-name">.vtj-icon-template
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-exit-fullscreen"></span>
            <div class="name">
              24gl-fullScreenEnter
            </div>
            <div class="code-name">.vtj-icon-exit-fullscreen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-fullscreen"></span>
            <div class="name">
              24gl-fullScreenEnter3
            </div>
            <div class="code-name">.vtj-icon-fullscreen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-edit"></span>
            <div class="name">
              编辑
            </div>
            <div class="code-name">.vtj-icon-edit
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-remove"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.vtj-icon-remove
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-js"></span>
            <div class="name">
              符号-JSX
            </div>
            <div class="code-name">.vtj-icon-js
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-database"></span>
            <div class="name">
              配比数据库
            </div>
            <div class="code-name">.vtj-icon-database
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-info"></span>
            <div class="name">
              信息空心
            </div>
            <div class="code-name">.vtj-icon-info
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-plus"></span>
            <div class="name">
              加_色块
            </div>
            <div class="code-name">.vtj-icon-plus
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-minus"></span>
            <div class="name">
              减_色块
            </div>
            <div class="code-name">.vtj-icon-minus
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-help"></span>
            <div class="name">
              帮助
            </div>
            <div class="code-name">.vtj-icon-help
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-vars"></span>
            <div class="name">
              代码
            </div>
            <div class="code-name">.vtj-icon-vars
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-outline"></span>
            <div class="name">
              大纲管理_默认
            </div>
            <div class="code-name">.vtj-icon-outline
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-visible"></span>
            <div class="name">
              明文显示
            </div>
            <div class="code-name">.vtj-icon-visible
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-invisible"></span>
            <div class="name">
              秘文显示
            </div>
            <div class="code-name">.vtj-icon-invisible
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-document"></span>
            <div class="name">
              文档-默认
            </div>
            <div class="code-name">.vtj-icon-document
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-history"></span>
            <div class="name">
              历史记录
            </div>
            <div class="code-name">.vtj-icon-history
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-fixed"></span>
            <div class="name">
              解除固定,图钉
            </div>
            <div class="code-name">.vtj-icon-fixed
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-unfixed"></span>
            <div class="name">
              解除固定,图钉
            </div>
            <div class="code-name">.vtj-icon-unfixed
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-search"></span>
            <div class="name">
              搜索
            </div>
            <div class="code-name">.vtj-icon-search
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-more"></span>
            <div class="name">
              more
            </div>
            <div class="code-name">.vtj-icon-more
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-close"></span>
            <div class="name">
              3.1关闭
            </div>
            <div class="code-name">.vtj-icon-close
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-components"></span>
            <div class="name">
              组件
            </div>
            <div class="code-name">.vtj-icon-components
            </div>
          </li>
          
          <li class="dib">
            <span class="icon VtjIconfont vtj-icon-block"></span>
            <div class="name">
              元数据-组件库
            </div>
            <div class="code-name">.vtj-icon-block
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="VtjIconfont vtj-icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            VtjIconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-chat-record"></use>
                </svg>
                <div class="name">历史对话</div>
                <div class="code-name">#vtj-icon-chat-record</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-new-chat"></use>
                </svg>
                <div class="name">新对话</div>
                <div class="code-name">#vtj-icon-new-chat</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-ai"></use>
                </svg>
                <div class="name">ai</div>
                <div class="code-name">#vtj-icon-ai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-uniapp"></use>
                </svg>
                <div class="name">uniapp</div>
                <div class="code-name">#vtj-icon-uniapp</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-window-max"></use>
                </svg>
                <div class="name">窗体-窗口化-细</div>
                <div class="code-name">#vtj-icon-window-max</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-window-min"></use>
                </svg>
                <div class="name">窗体-窗体化-细</div>
                <div class="code-name">#vtj-icon-window-min</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-window-close"></use>
                </svg>
                <div class="name">窗体-关闭-细</div>
                <div class="code-name">#vtj-icon-window-close</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-window-normal"></use>
                </svg>
                <div class="name">窗体-窗口最大化-细</div>
                <div class="code-name">#vtj-icon-window-normal</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-window-down"></use>
                </svg>
                <div class="name">窗体-折叠</div>
                <div class="code-name">#vtj-icon-window-down</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-window-up"></use>
                </svg>
                <div class="name">窗体-展开</div>
                <div class="code-name">#vtj-icon-window-up</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-np-save"></use>
                </svg>
                <div class="name">保存</div>
                <div class="code-name">#vtj-icon-np-save</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-np-file"></use>
                </svg>
                <div class="name">查看文件</div>
                <div class="code-name">#vtj-icon-np-file</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-np-edit"></use>
                </svg>
                <div class="name">编辑</div>
                <div class="code-name">#vtj-icon-np-edit</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-np-share"></use>
                </svg>
                <div class="name">冲销</div>
                <div class="code-name">#vtj-icon-np-share</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-np-search"></use>
                </svg>
                <div class="name">查询</div>
                <div class="code-name">#vtj-icon-np-search</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-np-export"></use>
                </svg>
                <div class="name">导出</div>
                <div class="code-name">#vtj-icon-np-export</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-np-import"></use>
                </svg>
                <div class="name">导入</div>
                <div class="code-name">#vtj-icon-np-import</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-np-list"></use>
                </svg>
                <div class="name">清单</div>
                <div class="code-name">#vtj-icon-np-list</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-np-print"></use>
                </svg>
                <div class="name">打印</div>
                <div class="code-name">#vtj-icon-np-print</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-np-cancel"></use>
                </svg>
                <div class="name">取消</div>
                <div class="code-name">#vtj-icon-np-cancel</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-np-confirm"></use>
                </svg>
                <div class="name">确认</div>
                <div class="code-name">#vtj-icon-np-confirm</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-np-reset"></use>
                </svg>
                <div class="name">全消</div>
                <div class="code-name">#vtj-icon-np-reset</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-np-return-all"></use>
                </svg>
                <div class="name">返回所有数据</div>
                <div class="code-name">#vtj-icon-np-return-all</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-np-return"></use>
                </svg>
                <div class="name">返回数据</div>
                <div class="code-name">#vtj-icon-np-return</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-np-remove"></use>
                </svg>
                <div class="name">删单</div>
                <div class="code-name">#vtj-icon-np-remove</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-np-remove-row"></use>
                </svg>
                <div class="name">删行</div>
                <div class="code-name">#vtj-icon-np-remove-row</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-np-delete"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#vtj-icon-np-delete</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-np-exit"></use>
                </svg>
                <div class="name">退出</div>
                <div class="code-name">#vtj-icon-np-exit</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-np-refresh"></use>
                </svg>
                <div class="name">刷新</div>
                <div class="code-name">#vtj-icon-np-refresh</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-np-add"></use>
                </svg>
                <div class="name">新单</div>
                <div class="code-name">#vtj-icon-np-add</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-np-select"></use>
                </svg>
                <div class="name">选择</div>
                <div class="code-name">#vtj-icon-np-select</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-np-add-row"></use>
                </svg>
                <div class="name">增行</div>
                <div class="code-name">#vtj-icon-np-add-row</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-np-extend"></use>
                </svg>
                <div class="name">向下展开</div>
                <div class="code-name">#vtj-icon-np-extend</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-np-close"></use>
                </svg>
                <div class="name">关闭</div>
                <div class="code-name">#vtj-icon-np-close</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-np-submit"></use>
                </svg>
                <div class="name">提交</div>
                <div class="code-name">#vtj-icon-np-submit</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-deps"></use>
                </svg>
                <div class="name">管理依赖</div>
                <div class="code-name">#vtj-icon-deps</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-back"></use>
                </svg>
                <div class="name">返回</div>
                <div class="code-name">#vtj-icon-back</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-home"></use>
                </svg>
                <div class="name">首页</div>
                <div class="code-name">#vtj-icon-home</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-api"></use>
                </svg>
                <div class="name">API管理</div>
                <div class="code-name">#vtj-icon-api</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-export"></use>
                </svg>
                <div class="name">导出</div>
                <div class="code-name">#vtj-icon-export</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-import"></use>
                </svg>
                <div class="name">导入</div>
                <div class="code-name">#vtj-icon-import</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-greater"></use>
                </svg>
                <div class="name">向右箭头</div>
                <div class="code-name">#vtj-icon-greater</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-smaller"></use>
                </svg>
                <div class="name">向左箭头</div>
                <div class="code-name">#vtj-icon-smaller</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-check"></use>
                </svg>
                <div class="name">正确</div>
                <div class="code-name">#vtj-icon-check</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-switch"></use>
                </svg>
                <div class="name">切换</div>
                <div class="code-name">#vtj-icon-switch</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-copy"></use>
                </svg>
                <div class="name">复制</div>
                <div class="code-name">#vtj-icon-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-lock"></use>
                </svg>
                <div class="name">lock</div>
                <div class="code-name">#vtj-icon-lock</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-unlock"></use>
                </svg>
                <div class="name">unlock</div>
                <div class="code-name">#vtj-icon-unlock</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-layers"></use>
                </svg>
                <div class="name">层级</div>
                <div class="code-name">#vtj-icon-layers</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-console"></use>
                </svg>
                <div class="name">code</div>
                <div class="code-name">#vtj-icon-console</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-team"></use>
                </svg>
                <div class="name">团队</div>
                <div class="code-name">#vtj-icon-team</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-publish"></use>
                </svg>
                <div class="name">icon_发布</div>
                <div class="code-name">#vtj-icon-publish</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-preview"></use>
                </svg>
                <div class="name">预览</div>
                <div class="code-name">#vtj-icon-preview</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-save"></use>
                </svg>
                <div class="name">保存</div>
                <div class="code-name">#vtj-icon-save</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-pc"></use>
                </svg>
                <div class="name">分类-电脑平板</div>
                <div class="code-name">#vtj-icon-pc</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-phone"></use>
                </svg>
                <div class="name">平板电脑,移动设备</div>
                <div class="code-name">#vtj-icon-phone</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-pad"></use>
                </svg>
                <div class="name">23C平板</div>
                <div class="code-name">#vtj-icon-pad</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-redo"></use>
                </svg>
                <div class="name">refresh-right</div>
                <div class="code-name">#vtj-icon-redo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-refresh"></use>
                </svg>
                <div class="name">refresh</div>
                <div class="code-name">#vtj-icon-refresh</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-undo"></use>
                </svg>
                <div class="name">refresh-left</div>
                <div class="code-name">#vtj-icon-undo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-category"></use>
                </svg>
                <div class="name">分类</div>
                <div class="code-name">#vtj-icon-category</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-project"></use>
                </svg>
                <div class="name">项目</div>
                <div class="code-name">#vtj-icon-project</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-notice"></use>
                </svg>
                <div class="name">通知</div>
                <div class="code-name">#vtj-icon-notice</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-fav"></use>
                </svg>
                <div class="name">收藏</div>
                <div class="code-name">#vtj-icon-fav</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-bug"></use>
                </svg>
                <div class="name">bug</div>
                <div class="code-name">#vtj-icon-bug</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-file"></use>
                </svg>
                <div class="name">24gl-fileText</div>
                <div class="code-name">#vtj-icon-file</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-folder"></use>
                </svg>
                <div class="name">24gl-folderOpen</div>
                <div class="code-name">#vtj-icon-folder</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-upload"></use>
                </svg>
                <div class="name">上传</div>
                <div class="code-name">#vtj-icon-upload</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-download"></use>
                </svg>
                <div class="name">操作-上传-download</div>
                <div class="code-name">#vtj-icon-download</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-user"></use>
                </svg>
                <div class="name">user</div>
                <div class="code-name">#vtj-icon-user</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-setting"></use>
                </svg>
                <div class="name">205设置-线性</div>
                <div class="code-name">#vtj-icon-setting</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-arrow-right"></use>
                </svg>
                <div class="name">箭头_向右</div>
                <div class="code-name">#vtj-icon-arrow-right</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-arrow-left"></use>
                </svg>
                <div class="name">箭头_向左</div>
                <div class="code-name">#vtj-icon-arrow-left</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-arrow-down"></use>
                </svg>
                <div class="name">箭头_向下</div>
                <div class="code-name">#vtj-icon-arrow-down</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-arrow-up"></use>
                </svg>
                <div class="name">箭头_向上</div>
                <div class="code-name">#vtj-icon-arrow-up</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-share"></use>
                </svg>
                <div class="name">分享</div>
                <div class="code-name">#vtj-icon-share</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-data"></use>
                </svg>
                <div class="name">数据库,数据</div>
                <div class="code-name">#vtj-icon-data</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-template"></use>
                </svg>
                <div class="name">模板</div>
                <div class="code-name">#vtj-icon-template</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-exit-fullscreen"></use>
                </svg>
                <div class="name">24gl-fullScreenEnter</div>
                <div class="code-name">#vtj-icon-exit-fullscreen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-fullscreen"></use>
                </svg>
                <div class="name">24gl-fullScreenEnter3</div>
                <div class="code-name">#vtj-icon-fullscreen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-edit"></use>
                </svg>
                <div class="name">编辑</div>
                <div class="code-name">#vtj-icon-edit</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-remove"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#vtj-icon-remove</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-js"></use>
                </svg>
                <div class="name">符号-JSX</div>
                <div class="code-name">#vtj-icon-js</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-database"></use>
                </svg>
                <div class="name">配比数据库</div>
                <div class="code-name">#vtj-icon-database</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-info"></use>
                </svg>
                <div class="name">信息空心</div>
                <div class="code-name">#vtj-icon-info</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-plus"></use>
                </svg>
                <div class="name">加_色块</div>
                <div class="code-name">#vtj-icon-plus</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-minus"></use>
                </svg>
                <div class="name">减_色块</div>
                <div class="code-name">#vtj-icon-minus</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-help"></use>
                </svg>
                <div class="name">帮助</div>
                <div class="code-name">#vtj-icon-help</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-vars"></use>
                </svg>
                <div class="name">代码</div>
                <div class="code-name">#vtj-icon-vars</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-outline"></use>
                </svg>
                <div class="name">大纲管理_默认</div>
                <div class="code-name">#vtj-icon-outline</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-visible"></use>
                </svg>
                <div class="name">明文显示</div>
                <div class="code-name">#vtj-icon-visible</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-invisible"></use>
                </svg>
                <div class="name">秘文显示</div>
                <div class="code-name">#vtj-icon-invisible</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-document"></use>
                </svg>
                <div class="name">文档-默认</div>
                <div class="code-name">#vtj-icon-document</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-history"></use>
                </svg>
                <div class="name">历史记录</div>
                <div class="code-name">#vtj-icon-history</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-fixed"></use>
                </svg>
                <div class="name">解除固定,图钉</div>
                <div class="code-name">#vtj-icon-fixed</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-unfixed"></use>
                </svg>
                <div class="name">解除固定,图钉</div>
                <div class="code-name">#vtj-icon-unfixed</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-search"></use>
                </svg>
                <div class="name">搜索</div>
                <div class="code-name">#vtj-icon-search</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-more"></use>
                </svg>
                <div class="name">more</div>
                <div class="code-name">#vtj-icon-more</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-close"></use>
                </svg>
                <div class="name">3.1关闭</div>
                <div class="code-name">#vtj-icon-close</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-components"></use>
                </svg>
                <div class="name">组件</div>
                <div class="code-name">#vtj-icon-components</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#vtj-icon-block"></use>
                </svg>
                <div class="name">元数据-组件库</div>
                <div class="code-name">#vtj-icon-block</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
