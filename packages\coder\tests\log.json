{"input": {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "data": {"project": {"__VTJ_PROJECT__": true, "__VERSION__": "1706682506703", "id": "@vtj/pro", "name": "VTJ.PRO", "homepage": "", "description": "VTJ.PRO", "dependencies": [{"package": "vue", "version": "latest", "library": "<PERSON><PERSON>", "urls": ["@vtj/materials/deps/vue/vue.global.prod.js"], "assetsLibrary": "VueMaterial", "required": true, "official": true, "enabled": true}, {"package": "vue-router", "version": "latest", "library": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "urls": ["@vtj/materials/deps/vue-router/vue-router.global.prod.js"], "assetsLibrary": "VueRouterMaterial", "required": true, "official": true, "enabled": true}, {"package": "@vtj/utils", "version": "latest", "library": "VtjUtils", "urls": ["@vtj/materials/deps/@vtj/utils/index.umd.js"], "required": true, "official": true, "enabled": true}, {"package": "@vtj/icons", "version": "latest", "library": "VtjIcons", "urls": ["@vtj/materials/deps/@vtj/icons/style.css", "@vtj/materials/deps/@vtj/icons/index.umd.js"], "required": true, "official": true, "enabled": true}, {"package": "element-plus", "version": "latest", "library": "ElementPlus", "urls": ["@vtj/materials/deps/element-plus/index.css", "@vtj/materials/deps/element-plus/index.full.min.js"], "assetsUrl": "@vtj/materials/assets/element/index.umd.js", "assetsLibrary": "ElementPlusMaterial", "required": false, "official": true, "enabled": true}, {"package": "echarts", "version": "latest", "library": "echarts", "urls": ["@vtj/materials/deps/echarts/echarts.min.js"], "required": false, "official": true, "enabled": true}, {"package": "@vtj/ui", "version": "latest", "library": "VtjUI", "urls": ["@vtj/materials/deps/@vtj/ui/style.css", "@vtj/materials/deps/@vtj/ui/index.umd.js"], "assetsUrl": "@vtj/materials/assets/ui/index.umd.js", "assetsLibrary": "VtjUIMaterial", "required": false, "official": true, "enabled": true}, {"package": "ant-design-vue", "version": "latest", "library": "antd", "urls": ["@vtj/materials/deps/ant-design-vue/rest.css", "@vtj/materials/deps/ant-design-vue/dayjs/dayjs.min.js", "@vtj/materials/deps/ant-design-vue/dayjs/plugin/customParseFormat.js", "@vtj/materials/deps/ant-design-vue/dayjs/plugin/weekday.js", "@vtj/materials/deps/ant-design-vue/dayjs/plugin/localeData.js", "@vtj/materials/deps/ant-design-vue/dayjs/plugin/weekOfYear.js", "@vtj/materials/deps/ant-design-vue/dayjs/plugin/weekYear.js", "@vtj/materials/deps/ant-design-vue/dayjs/plugin/advancedFormat.js", "@vtj/materials/deps/ant-design-vue/dayjs/plugin/quarterOfYear.js", "@vtj/materials/deps/ant-design-vue/antd.min.js"], "assetsUrl": "@vtj/materials/assets/antdv/index.umd.js", "assetsLibrary": "AntdvMaterial", "required": false, "official": true, "enabled": true}], "pages": [], "blocks": [{"name": "Aaa", "title": "aaa", "type": "block", "id": "2lr5v<PERSON><PERSON><PERSON>h6"}], "apis": []}, "dsl": {"name": "Aaa", "locked": false, "inject": [], "state": {}, "lifeCycles": {}, "methods": {}, "computed": {}, "watch": [], "css": "", "props": [], "emits": [], "slots": [], "dataSources": {}, "__VTJ_BLOCK__": true, "__VERSION__": "1706682506703", "id": "2lr5v<PERSON><PERSON><PERSON>h6", "nodes": [{"id": "2k9zqm3ukw6", "name": "Transition", "from": "vue", "invisible": false, "locked": false, "children": [{"id": "105ierhhix6", "name": "img", "from": "", "invisible": false, "locked": false, "children": [], "props": {"src": "/@fs/D:/vtj/vtj-next/vtj/packages/designer/src/assets/logo.png", "width": "200", "height": "200"}, "directives": [], "events": {}}], "props": {}, "directives": [], "events": {}}, {"id": "2gqdswbbsks", "name": "RouterLink", "from": "vue-router", "invisible": false, "locked": false, "children": "RouterLink", "props": {"to": "/?a=3", "replace": true}, "directives": [], "events": {}}]}}}, "error": {"loc": {"start": {"line": 3, "column": 117}, "end": {"line": 3, "column": 123}}, "cause": {"span": {"start": {"file": {"content": "\n<template>\n<Transition   ><img  src=\"/@fs/D:/vtj/vtj-next/vtj/packages/designer/src/assets/logo.png\" width=\"200\" height=\"200\" ></img></Transition>\n<RouterLink  to=\"/?a=3\" :replace='true' >RouterLink</RouterLink>\n</template>\n<script lang=\"ts\">\nimport { defineComponent, reactive, Transition } from 'vue';\nimport { RouterLink } from 'vue-router';\nimport { useProvider } from '@vtj/renderer';\nexport default defineComponent({\n  name: 'Aaa',\n  components: { Transition, img, RouterLink },\n  setup(props) {\n    const provider = useProvider({\n      id: '2lr5vnuyjh6',\n      version: '1706682506703'\n    });\n    const state = reactive({});\n    return { state, props, provider };\n  }\n});\n\n</script>\n<style lang=\"scss\" scoped>\n\n</style>\n", "url": "angular-html-parser"}, "offset": 128, "line": 2, "col": 116}, "end": {"file": {"content": "\n<template>\n<Transition   ><img  src=\"/@fs/D:/vtj/vtj-next/vtj/packages/designer/src/assets/logo.png\" width=\"200\" height=\"200\" ></img></Transition>\n<RouterLink  to=\"/?a=3\" :replace='true' >RouterLink</RouterLink>\n</template>\n<script lang=\"ts\">\nimport { defineComponent, reactive, Transition } from 'vue';\nimport { RouterLink } from 'vue-router';\nimport { useProvider } from '@vtj/renderer';\nexport default defineComponent({\n  name: 'Aaa',\n  components: { Transition, img, RouterLink },\n  setup(props) {\n    const provider = useProvider({\n      id: '2lr5vnuyjh6',\n      version: '1706682506703'\n    });\n    const state = reactive({});\n    return { state, props, provider };\n  }\n});\n\n</script>\n<style lang=\"scss\" scoped>\n\n</style>\n", "url": "angular-html-parser"}, "offset": 134, "line": 2, "col": 122}, "fullStart": {"file": {"content": "\n<template>\n<Transition   ><img  src=\"/@fs/D:/vtj/vtj-next/vtj/packages/designer/src/assets/logo.png\" width=\"200\" height=\"200\" ></img></Transition>\n<RouterLink  to=\"/?a=3\" :replace='true' >RouterLink</RouterLink>\n</template>\n<script lang=\"ts\">\nimport { defineComponent, reactive, Transition } from 'vue';\nimport { RouterLink } from 'vue-router';\nimport { useProvider } from '@vtj/renderer';\nexport default defineComponent({\n  name: 'Aaa',\n  components: { Transition, img, RouterLink },\n  setup(props) {\n    const provider = useProvider({\n      id: '2lr5vnuyjh6',\n      version: '1706682506703'\n    });\n    const state = reactive({});\n    return { state, props, provider };\n  }\n});\n\n</script>\n<style lang=\"scss\" scoped>\n\n</style>\n", "url": "angular-html-parser"}, "offset": 128, "line": 2, "col": 116}, "details": null}, "msg": "Void elements do not have end tags \"img\"", "level": 1, "elementName": "img"}, "codeFrame": "  1 |\n  2 | <template>\n> 3 | <Transition   ><img  src=\"/@fs/D:/vtj/vtj-next/vtj/packages/designer/src/assets/logo.png\" width=\"200\" height=\"200\" ></img></Transition>\n    |                                                                                                                     ^^^^^^\n  4 | <RouterLink  to=\"/?a=3\" :replace='true' >RouterLink</RouterLink>\n  5 | </template>\n  6 | <script lang=\"ts\">"}}