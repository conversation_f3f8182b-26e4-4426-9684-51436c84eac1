// ElementPlus 公共变量
$color-white: #ffffff !default;
$color-black: #000000 !default;
$color-primary: #409eff !default;
$color-primary-light-3: #79bbff !default;
$color-primary-light-5: #a0cfff !default;
$color-primary-light-7: #c6e2ff !default;
$color-primary-light-8: #d9ecff !default;
$color-primary-light-9: #ecf5ff !default;
$color-primary-dark-2: #337ecc !default;
$color-success: #67c23a !default;
$color-success-light-3: #95d475 !default;
$color-success-light-5: #b3e19d !default;
$color-success-light-7: #d1edc4 !default;
$color-success-light-8: #e1f3d8 !default;
$color-success-light-9: #f0f9eb !default;
$color-success-dark-2: #529b2e !default;
$color-warning: #e6a23c !default;
$color-warning-light-3: #eebe77 !default;
$color-warning-light-5: #f3d19e !default;
$color-warning-light-7: #f8e3c5 !default;
$color-warning-light-8: #faecd8 !default;
$color-warning-light-9: #fdf6ec !default;
$color-warning-dark-2: #b88230 !default;
$color-danger: #f56c6c !default;
$color-danger-light-3: #f89898 !default;
$color-danger-light-5: #fab6b6 !default;
$color-danger-light-7: #fcd3d3 !default;
$color-danger-light-8: #fde2e2 !default;
$color-danger-light-9: #fef0f0 !default;
$color-danger-dark-2: #c45656 !default;
$color-error: #f56c6c !default;
$color-error-light-3: #f89898 !default;
$color-error-light-5: #fab6b6 !default;
$color-error-light-7: #fcd3d3 !default;
$color-error-light-8: #fde2e2 !default;
$color-error-light-9: #fef0f0 !default;
$color-error-dark-2: #c45656 !default;
$color-info: #909399 !default;
$color-info-light-3: #b1b3b8 !default;
$color-info-light-5: #c8c9cc !default;
$color-info-light-7: #dedfe0 !default;
$color-info-light-8: #e9e9eb !default;
$color-info-light-9: #f4f4f5 !default;
$color-info-dark-2: #73767a !default;
$bg-color: #ffffff !default;
$bg-color-page: #f2f3f5 !default;
$bg-color-overlay: #ffffff !default;
$text-color-primary: #303133 !default;
$text-color-regular: #606266 !default;
$text-color-secondary: #909399 !default;
$text-color-placeholder: #a8abb2 !default;
$text-color-disabled: #c0c4cc !default;
$border-color: #dcdfe6 !default;
$border-color-light: #e4e7ed !default;
$border-color-lighter: #ebeef5 !default;
$border-color-extra-light: #f2f6fc !default;
$border-color-dark: #d4d7de !default;
$border-color-darker: #cdd0d6 !default;
$fill-color: #f0f2f5 !default;
$fill-color-light: #f5f7fa !default;
$fill-color-lighter: #fafafa !default;
$fill-color-extra-light: #fafcff !default;
$fill-color-dark: #ebedf0 !default;
$fill-color-darker: #e6e8eb !default;
$fill-color-blank: #ffffff !default;
$box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.04),
  0px 8px 20px rgba(0, 0, 0, 0.08) !default;
$box-shadow-light: 0px 0px 12px rgba(0, 0, 0, 0.12) !default;
$box-shadow-lighter: 0px 0px 6px rgba(0, 0, 0, 0.12) !default;
$box-shadow-dark: 0px 16px 48px 16px rgba(0, 0, 0, 0.08),
  0px 12px 32px rgba(0, 0, 0, 0.12), 0px 8px 16px -8px rgba(0, 0, 0, 0.16) !default;
$disabled-bg-color: $fill-color-light !default;
$disabled-text-color: $text-color-placeholder !default;
$disabled-border-color: $border-color-light !default;
$overlay-color: rgba(0, 0, 0, 0.8) !default;
$overlay-color-light: rgba(0, 0, 0, 0.7) !default;
$overlay-color-lighter: rgba(0, 0, 0, 0.5) !default;
$mask-color: rgba(255, 255, 255, 0.9) !default;
$mask-color-extra-light: rgba(255, 255, 255, 0.3) !default;
$border-width: 1px !default;
$border-style: solid !default;
$border-color-hover: $text-color-disabled !default;
$border: $border-width $border-style $border-color !default;
$svg-monochrome-grey: $border-color !default;
