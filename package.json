{"name": "vtj", "private": true, "description": "基于 Vue3 + Typescript 的低代码页面可视化设计器。内置低代码引擎、渲染器和代码生成器，面向前端开发者，开箱即用。 无缝嵌入本地开发工程，不改变前端开发流程和编码习惯。", "devDependencies": {"pnpm": "~10.13.0", "lerna": "~8.2.0", "axios": "~1.10.0", "fs-extra": "~11.3.0", "jsdom": "~26.1.0", "typedoc": "~0.28.3", "typescript": "~5.8.2", "ts-node": "~10.9.2", "nodemon": "~3.1.9", "typedoc-plugin-markdown": "~4.7.0", "git-cz": "~4.9.0", "husky": "~9.1.1", "@commitlint/cli": "~19.8.0", "@commitlint/config-conventional": "~19.8.0", "mime": "~4.0.1", "node-ssh": "~13.2.0"}, "scripts": {"setup": "pnpm install --unsafe-perm --registry=https://registry.npmmirror.com", "dev": "cd dev && npm run dev", "dev:uni": "cd dev && npm run dev:uni", "dev:build": "cd dev && npm run build && npm run build:uni", "dev:build-uni": "cd dev && npm run build:uni", "dev:preview": "cd dev && npm run preview", "clean": "lerna clean -y && node scripts/clean.mjs", "reset": "pnpm run clean && pnpm run setup", "outdate": "pnpm outdated -r", "update": "pnpm up -r --unsafe-perm --registry=https://registry.npmmirror.com", "sync": "node scripts/sync.mjs", "downloads": "node scripts/downloads.mjs", "test": "lerna run --no-private test", "build": "lerna run --no-private build", "coverage": "lerna run --no-private coverage", "typedoc": "typedoc --plugin typedoc-plugin-markdown", "ci": "git add . && git commit -m \"ci: 👷commit \" && git push", "commit": "git add . && git-cz", "prepare": "husky install", "prerelease": "lerna version prerelease --yes && npm run build && npm run ci && pnpm -r publish  --access public --tag test --no-git-checks && pnpm run sync", "patch": "lerna version patch --yes && npm run build && npm run ci && npm run publish && pnpm run sync", "minor": "lerna version minor --yes && npm run build && npm run ci && npm run publish && pnpm run sync", "publish": "pnpm -r publish  --access public --publish-branch next --report-summary --no-git-checks", "ssh": "node scripts/ssh.mjs", "cli:test": "cd packages/cli && npm run vitest", "cli:build": "cd packages/cli && npm run build", "base:test": "cd packages/base && npm run vitest", "base:build": "cd packages/base && npm run build", "utils:test": "cd packages/utils && npm run vitest", "utils:build": "cd packages/utils && npm run build", "core:test": "cd packages/core && npm run vitest", "core:build": "cd packages/core && npm run build", "coder:test": "cd packages/coder && npm run vitest", "coder:build": "cd packages/coder && npm run build", "parser:test": "cd packages/parser && npm run vitest", "parser:build": "cd packages/parser && npm run build", "designer:test": "cd packages/designer && npm run vitest", "designer:build": "cd packages/designer && npm run build", "renderer:test": "cd packages/renderer && npm run vitest", "renderer:build": "cd packages/renderer && npm run build", "uni:test": "cd packages/uni && npm run vitest", "uni:build": "cd packages/uni && npm run build", "local:test": "cd packages/local && npm run vitest", "local:build": "cd packages/local && npm run build", "materials:test": "cd packages/materials && npm run vitest", "materials:build": "cd packages/materials && npm run build", "node:test": "cd packages/node && npm run vitest", "node:build": "cd packages/node && npm run build", "ui:test": "cd packages/ui && npm run vitest", "ui:build": "cd packages/ui && npm run build", "charts:test": "cd packages/charts && npm run vitest", "charts:build": "cd packages/charts && npm run build", "uitls:test": "cd packages/uitls && npm run vitest", "uitls:build": "cd packages/uitls && npm run build", "icons:test": "cd packages/icons && npm run vitest", "icons:build": "cd packages/icons && npm run build", "vtj:dev": "node create-vtj/dist/index.mjs", "vtj:build": "cd create-vtj && npm run build", "docs:dev": "cd docs && vitepress dev", "docs:build": "npm run typedoc && cd docs && npm run docs:build", "docs:preview": "cd docs && vitepress preview", "pro:dev": "cd platforms/pro && npm run dev", "pro:build": "cd platforms/pro && npm run build", "pro:preview": "cd platforms/pro && npm run preview", "pro-uni:dev": "cd platforms/pro-uni && npm run dev", "pro-uni:build": "cd platforms/pro-uni && npm run build", "mui:dev": "cd platforms/mobile && npm run dev", "mui:app": "cd platforms/mobile && npm run dev:app", "mui:build": "cd platforms/mobile && npm run build && npm run build:h5", "mui:preview": "cd platforms/mobile && npm run preview", "web:build": "cd platforms/web && npm run build", "app:dev": "cd apps/app && npm run dev", "app:build": "cd apps/app  && npm run build", "app:preview": "cd apps/app && npm run preview", "h5:dev": "cd apps/h5 && npm run dev", "h5:build": "cd apps/h5  && npm run build", "h5:preview": "cd apps/h5 && npm run preview", "uniapp:dev": "cd apps/uniapp && npm run dev:h5", "uniapp:mp": "cd apps/uniapp && npm run dev:mp-weixin", "uniapp:build": "cd apps/uniapp && npm run build:h5", "uniapp:preview": "cd apps/uniapp && npm run preview:h5", "plugin:dev": "cd apps/plugin && npm run dev", "plugin:test": "cd apps/plugin && npm run test", "plugin:build": "cd apps/plugin && npm run build", "mate:dev": "cd apps/material && npm run dev", "ext:dev": "cd apps/extension && npm run dev"}, "engines": {"node": ">=20.0.0", "lerna": ">=8.0.0", "pnpm": ">=8.0.0"}, "publishConfig": {"access": "public"}, "config": {"commitizen": {"path": "git-cz"}}, "nodemonConfig": {"watch": ["create-vtj/src"], "ext": "js,ts,mts,cts,mjs,cjs,json"}}