export default [
  {
    text: '基础元件',
    base: '/ui',
    items: [
      { text: 'XIcon 图标', link: '/icon' },
      { text: 'XContainer 容器', link: '/container' },
      { text: 'XAction 操作按钮', link: '/action' },
      { text: 'XActionBar 操作按钮集', link: '/actionBar' },
      { text: 'XQrCode 二维码', link: '/qrcode' },
      { text: 'XImportButton 导入按钮', link: '/importButton' }
    ]
  },
  {
    text: '布局排版',
    base: '/ui',
    items: [
      { text: 'XPanel 面板', link: '/panel' },
      { text: 'XDialog 弹窗', link: '/dialog' },
      { text: 'XHeader 标题头', link: '/header' },
      { text: 'XTabs 选项卡', link: '/tabs' }
    ]
  },
  {
    text: '表单相关',
    base: '/ui',
    items: [
      { text: 'XDialogForm 弹窗表单', link: '/dialogForm' },
      { text: 'XField 字段', link: '/field' },
      { text: 'XForm 表单', link: '/form' },
      { text: 'XQueryForm 查询表单', link: '/queryForm' },
      { text: 'XPicker 数据选择器', link: '/picker' },
      { text: 'XDialogGrid 弹窗表格', link: '/dialogGrid' },
      { text: 'XCaptcha 图形验证码', link: '/captcha' },
      { text: 'XVerify 短信验证码', link: '/verify' }
    ]
  },
  {
    text: '数据展示',
    base: '/ui',
    items: [
      { text: 'XAttachment 附件', link: '/attachment' },
      { text: 'XDataItem 数据项', link: '/dataItem' },
      { text: 'XGrid 表格', link: '/grid' },
      { text: 'XList 列表', link: '/list' }
    ]
  }
];
