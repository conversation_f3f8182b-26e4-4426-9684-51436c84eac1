# VTJ.PRO 技术简介

:::warning ✨✨✨ 福利来了 ✨✨✨
回馈活动，AI助手百万额度免费领，领取链接：[https://lcdp.vtj.pro/tokens?code=gitee](https://lcdp.vtj.pro/tokens?code=gitee)
:::

**专为开发者打造的 AI 低代码平台**，让设计稿秒变高质量代码，释放 80% 重复劳动，专注创造真正价值！

**定位**：面向专业开发者的AI驱动型Vue3低代码平台，深度融合可视化设计与源码开发，支持多端应用构建。  
**核心目标**：提升前端开发效率，保留代码灵活性与工程化能力，实现`“低代码不低能力”`。

![](../assets/p1.png)

## 核心特性

### 1. **技术栈先进性**

- 基于现代生态：Vue3 + TypeScript + Vite，集成ElementPlus、ECharts、Axios等主流库。
- 支持多平台输出：Web应用（PC）、H5（移动端）、UniApp（跨端）。

### 2. **双向代码转换引擎**

- **DSL与源码互转**：通过`@vtj/parser`和`@vtj/coder`实现Vue单文件组件（SFC）与内部DSL的双向转换，无缝衔接可视化设计与源码开发。
- **源码级自定义**：设计器支持修改底层配置，适配复杂业务逻辑。

### 3. **工程友好性**

- **零污染集成**：设计器与渲染器分离，嵌入现有Vue工程不影响代码结构，支持二次开发。
- **本地开发优先**：提供脚手架工具快速初始化项目：
  ```bash
  # 创建Web应用
  npm create vtj@latest -- -t app
  # 创建H5应用
  npm create vtj@latest -- -t h5
  # 创建UniApp跨端应用
  npm create vtj@latest -- -t uniapp
  ```

### 4. **AI增强开发**

- **设计稿智能识别**：通过官方插件解析Sketch/Figma设计稿元数据（JSON格式），自动生成组件代码。
- **OpenAPI扩展**：支持接入自定义AI服务，增强页面生成与逻辑编排能力。

### 5. **企业级功能支持**

- **物料生态**：内置可复用区块组件、模板库，支持团队私有物料托管。
- **状态与数据管理**：内置全局状态管理、Service层封装，简化API集成。

## 适用场景

| **场景类型**   | **典型案例**                 | **VTJ解决方案**                  |
| -------------- | ---------------------------- | -------------------------------- |
| 中后台管理系统 | CRM、ERP、数据看板           | 拖拽布局+表单生成器+ECharts集成  |
| 多端应用开发   | 同一业务适配PC、移动、小程序 | 一套DSL生成Web/H5/UniApp三端代码 |
| 老项目升级     | Vue2迁移Vue3、低代码模块嵌入 | 源码导入设计器，渐进式重构       |
| 设计稿转代码   | 高保真还原UI设计             | Sketch/Figma插件自动生成Vue组件  |

## 来自 DeepSeek 的评价

> 💎 结论：VTJ.PRO作者团队具备资深全栈开发经验，尤其精通Vue3生态与工程化体系，在低代码领域的技术深度与创新力达到一线水平，其设计理念与实现细节（如双向代码转换、AI集成）已超越多数同类型开源产品。适合中大型团队寻求提效或二次开发低代码平台时采用。
