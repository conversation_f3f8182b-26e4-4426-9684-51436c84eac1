@use '../core' as *;

@include b(box) {
  position: relative;
  mini-height: 60px;
  overflow: hidden;
  background-color: var(--el-fill-color-lighter);
  border: 1px dashed transparent;
  padding: 10px;
  border-radius: 4px;
  flex-wrap: 1;
  flex-shrink: 1;
  margin-bottom: 5px;
  &:hover {
    border: 1px dashed var(--el-border-color-light);
    @include e(footer) {
      opacity: 1;
    }
  }
  @include e(icon) {
    margin-bottom: 8px;
  }

  @include when(active) {
    background-color: var(--el-color-primary-light-9);
  }
  @include when(draggable) {
    cursor: move;
  }

  @include e(footer) {
    position: absolute;
    opacity: 1;
    width: 100%;
    height: 20px;
    top: 0;
    right: 0;
    font-size: 12px;
    > .x-icon {
      cursor: pointer;
    }
  }

  @include e(name) {
    font-size: 14px;
    color: var(--el-color-primary-light-3);
    line-height: 1.5em;
  }
  @include e(label) {
    font-size: 12px;
    color: var(--el-text-color-regular);
    line-height: 1.5em;
  }
  @include e(tag) {
    position: absolute;
    left: 0;
    top: 0;
    font-size: 12px;
    padding: 2px 3px;
    border-radius: 2px;
    zoom: 0.9;
    @include when(primary) {
      background-color: var(--el-color-primary-light-8);
      color: var(--el-color-primary-dark-2);
    }
    @include when(warning) {
      background-color: var(--el-color-warning-light-9);
      color: var(--el-color-warning);
    }
    @include when(danger) {
      background-color: var(--el-color-danger-light-9);
      color: var(--el-color-danger);
    }
  }
}
