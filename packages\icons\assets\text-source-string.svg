<?xml version="1.0" encoding="UTF-8"?>
<svg width="12px" height="14px" viewBox="0 0 12 14" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>类型/文本</title>
    <g id="4.0icon" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="类型/文本" transform="translate(1.000000, 1.000000)" stroke="currentColor">
            <rect id="矩形" stroke-linejoin="round" x="0" y="0" width="10" height="12" rx="2"></rect>
            <line x1="2.5" y1="7.5" x2="7.5" y2="7.5" id="路径-18" stroke-linecap="round"></line>
            <line x1="3.5" y1="9.5" x2="6.5" y2="9.5" id="路径-18备份" stroke-linecap="round"></line>
        </g>
    </g>
</svg>