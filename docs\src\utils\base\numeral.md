# numeral 数字格式化  



| 函数名       | 描述                          | 类型                                              | 参数                                              | 返回值   |
| ------------ | ----------------------------- | ------------------------------------------------- | ------------------------------------------------- | -------- |
| numberFormat | 数字格式化，默认2位小数       | (val:number,format:string)=> number               | `val:数值,format:小数位数`                        | `number` |
| toFixed      | 保留小数点，默认2位           | (val:number,format:string, flag:boolean)=> number | (数值`val`, 小数位数`format`,是否四舍五入`round`) | `number` |
| numeral      | [文档](http://numeraljs.com/) | -                                                 | -                                                 | -        |



### 示例

:::preview
demo-preview=../../examples/utils/numeral/numeral-1.vue
:::
