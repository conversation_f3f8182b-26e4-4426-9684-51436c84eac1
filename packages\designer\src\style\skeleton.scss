@use 'core' as *;

@include b(skeleton) {
  font-size: 14px;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: getCssVar('bg-color');
  font-size: 14px;
  color: getCssVar('text-color-primary');
  font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
  * {
    box-sizing: border-box;
  }

  @include e(header) {
    padding: 0 !important;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
  }
  @include e(main) {
    border-left: 1px solid getCssVar('border-color');
    border-right: 1px solid getCssVar('border-color');
    padding: 0 !important;
  }
  @include e(wrapper) {
    border-top: 1px solid getCssVar('border-color');
    border-bottom: 1px solid getCssVar('border-color');
  }
  @include e(footer) {
    padding: 0 !important;
    font-size: 12px;
    align-items: center;
    display: flex;
  }
  @include e(left) {
    @include when(resizing) {
      border-right: 2px solid getCssVar('color-primary');
    }
    @include when(collapsed) {
      width: 49px !important;
      overflow: hidden;
    }
  }
  @include e(right) {
    @include when(resizing) {
      border-left: 2px solid getCssVar('color-primary');
    }
  }
}
