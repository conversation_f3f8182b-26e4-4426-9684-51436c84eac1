import { h } from 'vue';
import type { RendererOptions } from 'vxe-table';
import { createCellRenderProps } from '../utils';
import { ElTag } from 'element-plus';
import { baseRendererOptions } from './input';

export const XTag: RendererOptions = {
  ...baseRendererOptions,
  cellClassName: 'x-grid__x-tag',
  renderDefault(renderOpts, params) {
    const { props, cellValue } = createCellRenderProps(renderOpts, params);
    return [h(
      ElTag,
      { size: 'small', type: 'primary', ...props },
      () => cellValue
    )];
  },
  renderCell(renderOpts, params) {
    const { props, cellValue } = createCellRenderProps(renderOpts, params);
    return [h(
      ElTag,
      { size: 'small', type: 'primary', ...props },
      () => cellValue
    )];
  }
};
