---
# https://vitepress.dev/reference/default-theme-home-page

title: AI 驱动的 Vue3 低代码开发平台

layout: home
hero:
  name: 'VTJ.PRO'
  text: AI + 低代码
  tagline: 所想即所见，融合双核优势，让设计秒生纯净代码
  image:
    src: /assets/logo-w.svg
  actions:
    - theme: brand
      text: ⚡ 快速开始
      link: /guide/
    - theme: alt
      text: 💡 在线体验
      link: https://lcdp.vtj.pro/page/92q671qifm
      target: new

features:
  - icon: ⚙️
    title: 主流技术栈
    details: 基于 Vue3 + TypeScript + Vite 构建，深度整合 ElementPlus、Axios、ECharts 等主流工具链，开箱即用。
  - icon: 🧩
    title: 源码级自定义
    details: 低代码设计器支持源码级自由定制，无缝实现传统编码开发的所有功能，满足深度个性化需求。
  - icon: 🚀
    title: 零适应成本
    details: 完全遵循前端开发习惯，Vue开发者无需额外学习，设计器与本地项目环境天然融合。
  - icon: 🔌
    title: 引擎化扩展
    details: 内置可拆解的低代码引擎，支持独立调用，快速构建自有低代码平台，扩展能力无上限。
  - icon: 💻
    title: 源码零污染
    details: 采用设计器-渲染器分离架构，产物为纯净Vue代码，支持直接二次开发，杜绝环境侵入。
  - icon: 📦
    title: 高复用物料库
    details: 内置多套企业级组件库+页面模板，提供可定制区块组件，大幅提升标准化开发效率。
  - icon: 🤖
    title: AI智能提效
    details: 支持自然语言描述需求、上传网页截图、Sketch/Figma设计稿生成Vue组件
  - icon: 🔄
    title: 双向代码转换
    details: 独创 DSL与Vue源码双向编译能力，保障低代码与手写代码的自由切换。
---

:::warning ✨✨✨ 福利来了 ✨✨✨
回馈活动，AI助手百万额度免费领，领取链接：[https://lcdp.vtj.pro/tokens?code=gitee](https://lcdp.vtj.pro/tokens?code=gitee)
:::
