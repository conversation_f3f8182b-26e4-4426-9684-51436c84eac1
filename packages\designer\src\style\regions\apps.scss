@use '../core' as *;

@include b(apps-region) {
  display: flex;
  height: 100%;
  overflow: hidden;

  @include e(header) {
    width: 50px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-right: 1px solid getCssVar('border-color');
    text-align: center;
    color: getCssVar('text-color-primary');
    flex-shrink: 0;
  }

  @include e(top){
    i {
      margin-left: -2px;
    }
  }

  @include e(icon) {
    padding: 8px 0;
    cursor: pointer;
    font-size: 20px;
    border-left: 2px solid transparent;
    border-top: 1px solid transparent;
    border-bottom: 1px solid transparent;
    &:first-child {
      border-top: 1px solid transparent;
    }

    &:hover {
      color: getCssVar('color-primary');
    }
    &.is-active {
      color: getCssVar('color-primary');
    }
    &.is-open {
      border-left: 2px solid getCssVar('color-primary');
      background-color: var(--el-bg-color);
      margin-right: -1px;
      border-top: 1px solid var(--el-border-color);
      border-bottom: 1px solid var(--el-border-color);
      &:first-child {
        border-top: 1px solid transparent;
      }

      i {
        margin-left: -3px;
      }
    }
  }

  @include e(panels) {
    flex-grow: 1;
    overflow: hidden;
    min-width: 160px;
  }
}
