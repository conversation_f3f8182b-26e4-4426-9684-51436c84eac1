@use 'core' as *;

@include b(dialog) {
  background-color: getCssVar('bg-color');
  position: fixed;
  pointer-events: initial;

  @include when(draggable) {
    & > .x-panel__header {
      cursor: move;
    }
  }

  @include when(maximized) {
    width: 100% !important;
    height: 100% !important;
    left: 0 !important;
    top: 0 !important;
  }

  @include when(minimized) {
    display: none;
  }

  @include e(wrapper) {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    pointer-events: none;
    overflow: hidden;
    z-index: 1000;
    @include when(minimized) {
      display: none;
    }
    @include when(dragging) {
      user-select: none;
      iframe {
        user-select: none;
        pointer-events: none;
      }
    }
  }

  @include e(modal) {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background-color: getCssVar('mask-color-extra-light');
    pointer-events: initial;
    opacity: 0.5;
  }

  @include e(frame) {
    width: 100%;
    height: 100%;
    display: block;
    border: none;
  }

  @include when(primary) {
    border: 2px solid getCssVar('color-primary');
    border-top: none;
    > .x-panel__header {
      background-color: getCssVar('color-primary');
      color: getCssVar('color-white');
      .x-header__content {
        color: getCssVar('color-white');
      }
      .x-action__inner.is-icon.is-background-hover {
        color: getCssVar('color-white');
        &:hover {
          @include when(primary) {
            background-color: rgba(255, 255, 255, 0.3);
            opacity: 1;
          }
          @include when(danger) {
            background-color: getCssVar('color-danger');
            opacity: 1;
          }
          @include when(warning) {
            background-color: getCssVar('color-warning');
            opacity: 1;
          }
        }
      }
    }
  }

  @include when(pure) {
    background-color: var(--el-fill-color-light);
    > .x-panel__body {
      padding: 0;
    }
  }
}

body.x-dialog-visible {
  overflow: hidden;
}
