import { createIconComponent } from './utils';
export const VtjIconChatRecord = createIconComponent('VtjIconChatRecord', 'vtj-icon-chat-record');
export const VtjIconNewChat = createIconComponent('VtjIconNewChat', 'vtj-icon-new-chat');
export const VtjIconAi = createIconComponent('VtjIconAi', 'vtj-icon-ai');
export const VtjIconUniapp = createIconComponent('VtjIconUniapp', 'vtj-icon-uniapp');
export const VtjIconWindowMax = createIconComponent('VtjIconWindowMax', 'vtj-icon-window-max');
export const VtjIconWindowMin = createIconComponent('VtjIconWindowMin', 'vtj-icon-window-min');
export const VtjIconWindowClose = createIconComponent('VtjIconWindowClose', 'vtj-icon-window-close');
export const VtjIconWindowNormal = createIconComponent('VtjIconWindowNormal', 'vtj-icon-window-normal');
export const VtjIconWindowDown = createIconComponent('VtjIconWindowDown', 'vtj-icon-window-down');
export const VtjIconWindowUp = createIconComponent('VtjIconWindowUp', 'vtj-icon-window-up');
export const VtjIconNpSave = createIconComponent('VtjIconNpSave', 'vtj-icon-np-save');
export const VtjIconNpFile = createIconComponent('VtjIconNpFile', 'vtj-icon-np-file');
export const VtjIconNpEdit = createIconComponent('VtjIconNpEdit', 'vtj-icon-np-edit');
export const VtjIconNpShare = createIconComponent('VtjIconNpShare', 'vtj-icon-np-share');
export const VtjIconNpSearch = createIconComponent('VtjIconNpSearch', 'vtj-icon-np-search');
export const VtjIconNpExport = createIconComponent('VtjIconNpExport', 'vtj-icon-np-export');
export const VtjIconNpImport = createIconComponent('VtjIconNpImport', 'vtj-icon-np-import');
export const VtjIconNpList = createIconComponent('VtjIconNpList', 'vtj-icon-np-list');
export const VtjIconNpPrint = createIconComponent('VtjIconNpPrint', 'vtj-icon-np-print');
export const VtjIconNpCancel = createIconComponent('VtjIconNpCancel', 'vtj-icon-np-cancel');
export const VtjIconNpConfirm = createIconComponent('VtjIconNpConfirm', 'vtj-icon-np-confirm');
export const VtjIconNpReset = createIconComponent('VtjIconNpReset', 'vtj-icon-np-reset');
export const VtjIconNpReturnAll = createIconComponent('VtjIconNpReturnAll', 'vtj-icon-np-return-all');
export const VtjIconNpReturn = createIconComponent('VtjIconNpReturn', 'vtj-icon-np-return');
export const VtjIconNpRemove = createIconComponent('VtjIconNpRemove', 'vtj-icon-np-remove');
export const VtjIconNpRemoveRow = createIconComponent('VtjIconNpRemoveRow', 'vtj-icon-np-remove-row');
export const VtjIconNpDelete = createIconComponent('VtjIconNpDelete', 'vtj-icon-np-delete');
export const VtjIconNpExit = createIconComponent('VtjIconNpExit', 'vtj-icon-np-exit');
export const VtjIconNpRefresh = createIconComponent('VtjIconNpRefresh', 'vtj-icon-np-refresh');
export const VtjIconNpAdd = createIconComponent('VtjIconNpAdd', 'vtj-icon-np-add');
export const VtjIconNpSelect = createIconComponent('VtjIconNpSelect', 'vtj-icon-np-select');
export const VtjIconNpAddRow = createIconComponent('VtjIconNpAddRow', 'vtj-icon-np-add-row');
export const VtjIconNpExtend = createIconComponent('VtjIconNpExtend', 'vtj-icon-np-extend');
export const VtjIconNpClose = createIconComponent('VtjIconNpClose', 'vtj-icon-np-close');
export const VtjIconNpSubmit = createIconComponent('VtjIconNpSubmit', 'vtj-icon-np-submit');
export const VtjIconDeps = createIconComponent('VtjIconDeps', 'vtj-icon-deps');
export const VtjIconBack = createIconComponent('VtjIconBack', 'vtj-icon-back');
export const VtjIconHome = createIconComponent('VtjIconHome', 'vtj-icon-home');
export const VtjIconApi = createIconComponent('VtjIconApi', 'vtj-icon-api');
export const VtjIconExport = createIconComponent('VtjIconExport', 'vtj-icon-export');
export const VtjIconImport = createIconComponent('VtjIconImport', 'vtj-icon-import');
export const VtjIconGreater = createIconComponent('VtjIconGreater', 'vtj-icon-greater');
export const VtjIconSmaller = createIconComponent('VtjIconSmaller', 'vtj-icon-smaller');
export const VtjIconCheck = createIconComponent('VtjIconCheck', 'vtj-icon-check');
export const VtjIconSwitch = createIconComponent('VtjIconSwitch', 'vtj-icon-switch');
export const VtjIconCopy = createIconComponent('VtjIconCopy', 'vtj-icon-copy');
export const VtjIconLock = createIconComponent('VtjIconLock', 'vtj-icon-lock');
export const VtjIconUnlock = createIconComponent('VtjIconUnlock', 'vtj-icon-unlock');
export const VtjIconLayers = createIconComponent('VtjIconLayers', 'vtj-icon-layers');
export const VtjIconConsole = createIconComponent('VtjIconConsole', 'vtj-icon-console');
export const VtjIconTeam = createIconComponent('VtjIconTeam', 'vtj-icon-team');
export const VtjIconPublish = createIconComponent('VtjIconPublish', 'vtj-icon-publish');
export const VtjIconPreview = createIconComponent('VtjIconPreview', 'vtj-icon-preview');
export const VtjIconSave = createIconComponent('VtjIconSave', 'vtj-icon-save');
export const VtjIconPc = createIconComponent('VtjIconPc', 'vtj-icon-pc');
export const VtjIconPhone = createIconComponent('VtjIconPhone', 'vtj-icon-phone');
export const VtjIconPad = createIconComponent('VtjIconPad', 'vtj-icon-pad');
export const VtjIconRedo = createIconComponent('VtjIconRedo', 'vtj-icon-redo');
export const VtjIconRefresh = createIconComponent('VtjIconRefresh', 'vtj-icon-refresh');
export const VtjIconUndo = createIconComponent('VtjIconUndo', 'vtj-icon-undo');
export const VtjIconCategory = createIconComponent('VtjIconCategory', 'vtj-icon-category');
export const VtjIconProject = createIconComponent('VtjIconProject', 'vtj-icon-project');
export const VtjIconNotice = createIconComponent('VtjIconNotice', 'vtj-icon-notice');
export const VtjIconFav = createIconComponent('VtjIconFav', 'vtj-icon-fav');
export const VtjIconBug = createIconComponent('VtjIconBug', 'vtj-icon-bug');
export const VtjIconFile = createIconComponent('VtjIconFile', 'vtj-icon-file');
export const VtjIconFolder = createIconComponent('VtjIconFolder', 'vtj-icon-folder');
export const VtjIconUpload = createIconComponent('VtjIconUpload', 'vtj-icon-upload');
export const VtjIconDownload = createIconComponent('VtjIconDownload', 'vtj-icon-download');
export const VtjIconUser = createIconComponent('VtjIconUser', 'vtj-icon-user');
export const VtjIconSetting = createIconComponent('VtjIconSetting', 'vtj-icon-setting');
export const VtjIconArrowRight = createIconComponent('VtjIconArrowRight', 'vtj-icon-arrow-right');
export const VtjIconArrowLeft = createIconComponent('VtjIconArrowLeft', 'vtj-icon-arrow-left');
export const VtjIconArrowDown = createIconComponent('VtjIconArrowDown', 'vtj-icon-arrow-down');
export const VtjIconArrowUp = createIconComponent('VtjIconArrowUp', 'vtj-icon-arrow-up');
export const VtjIconShare = createIconComponent('VtjIconShare', 'vtj-icon-share');
export const VtjIconData = createIconComponent('VtjIconData', 'vtj-icon-data');
export const VtjIconTemplate = createIconComponent('VtjIconTemplate', 'vtj-icon-template');
export const VtjIconExitFullscreen = createIconComponent('VtjIconExitFullscreen', 'vtj-icon-exit-fullscreen');
export const VtjIconFullscreen = createIconComponent('VtjIconFullscreen', 'vtj-icon-fullscreen');
export const VtjIconEdit = createIconComponent('VtjIconEdit', 'vtj-icon-edit');
export const VtjIconRemove = createIconComponent('VtjIconRemove', 'vtj-icon-remove');
export const VtjIconJs = createIconComponent('VtjIconJs', 'vtj-icon-js');
export const VtjIconDatabase = createIconComponent('VtjIconDatabase', 'vtj-icon-database');
export const VtjIconInfo = createIconComponent('VtjIconInfo', 'vtj-icon-info');
export const VtjIconPlus = createIconComponent('VtjIconPlus', 'vtj-icon-plus');
export const VtjIconMinus = createIconComponent('VtjIconMinus', 'vtj-icon-minus');
export const VtjIconHelp = createIconComponent('VtjIconHelp', 'vtj-icon-help');
export const VtjIconVars = createIconComponent('VtjIconVars', 'vtj-icon-vars');
export const VtjIconOutline = createIconComponent('VtjIconOutline', 'vtj-icon-outline');
export const VtjIconVisible = createIconComponent('VtjIconVisible', 'vtj-icon-visible');
export const VtjIconInvisible = createIconComponent('VtjIconInvisible', 'vtj-icon-invisible');
export const VtjIconDocument = createIconComponent('VtjIconDocument', 'vtj-icon-document');
export const VtjIconHistory = createIconComponent('VtjIconHistory', 'vtj-icon-history');
export const VtjIconFixed = createIconComponent('VtjIconFixed', 'vtj-icon-fixed');
export const VtjIconUnfixed = createIconComponent('VtjIconUnfixed', 'vtj-icon-unfixed');
export const VtjIconSearch = createIconComponent('VtjIconSearch', 'vtj-icon-search');
export const VtjIconMore = createIconComponent('VtjIconMore', 'vtj-icon-more');
export const VtjIconClose = createIconComponent('VtjIconClose', 'vtj-icon-close');
export const VtjIconComponents = createIconComponent('VtjIconComponents', 'vtj-icon-components');
export const VtjIconBlock = createIconComponent('VtjIconBlock', 'vtj-icon-block');

/**
 * VtjIconChatRecord, VtjIconNewChat, VtjIconAi, VtjIconUniapp, VtjIconWindowMax, VtjIconWindowMin, VtjIconWindowClose, VtjIconWindowNormal, VtjIconWindowDown, VtjIconWindowUp, VtjIconNpSave, VtjIconNpFile, VtjIconNpEdit, VtjIconNpShare, VtjIconNpSearch, VtjIconNpExport, VtjIconNpImport, VtjIconNpList, VtjIconNpPrint, VtjIconNpCancel, VtjIconNpConfirm, VtjIconNpReset, VtjIconNpReturnAll, VtjIconNpReturn, VtjIconNpRemove, VtjIconNpRemoveRow, VtjIconNpDelete, VtjIconNpExit, VtjIconNpRefresh, VtjIconNpAdd, VtjIconNpSelect, VtjIconNpAddRow, VtjIconNpExtend, VtjIconNpClose, VtjIconNpSubmit, VtjIconDeps, VtjIconBack, VtjIconHome, VtjIconApi, VtjIconExport, VtjIconImport, VtjIconGreater, VtjIconSmaller, VtjIconCheck, VtjIconSwitch, VtjIconCopy, VtjIconLock, VtjIconUnlock, VtjIconLayers, VtjIconConsole, VtjIconTeam, VtjIconPublish, VtjIconPreview, VtjIconSave, VtjIconPc, VtjIconPhone, VtjIconPad, VtjIconRedo, VtjIconRefresh, VtjIconUndo, VtjIconCategory, VtjIconProject, VtjIconNotice, VtjIconFav, VtjIconBug, VtjIconFile, VtjIconFolder, VtjIconUpload, VtjIconDownload, VtjIconUser, VtjIconSetting, VtjIconArrowRight, VtjIconArrowLeft, VtjIconArrowDown, VtjIconArrowUp, VtjIconShare, VtjIconData, VtjIconTemplate, VtjIconExitFullscreen, VtjIconFullscreen, VtjIconEdit, VtjIconRemove, VtjIconJs, VtjIconDatabase, VtjIconInfo, VtjIconPlus, VtjIconMinus, VtjIconHelp, VtjIconVars, VtjIconOutline, VtjIconVisible, VtjIconInvisible, VtjIconDocument, VtjIconHistory, VtjIconFixed, VtjIconUnfixed, VtjIconSearch, VtjIconMore, VtjIconClose, VtjIconComponents, VtjIconBlock 
 */
