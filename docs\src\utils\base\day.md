# day 日期时间  dayjs



| 函数名     | 描述       | 类型                                        | 参数                                                                   | 返回值   |
| ---------- | ---------- | ------------------------------------------- | ---------------------------------------------------------------------- | -------- |
| dateFormat | 时间格式化 | `(val:<string \| number \| Date>)=> string` | (date：要转换的时间, format: 转换成那种格式 默认`YYYY-MM-DD HH-mm-ss`) | `String` |
| dayjs      | dayjs      | [文档](https://day.js.org/)                 |



### 示例

:::preview
demo-preview=../../examples/utils/day/day-1.vue
:::
