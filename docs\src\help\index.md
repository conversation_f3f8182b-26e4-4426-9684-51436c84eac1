---
# https://vitepress.dev/reference/default-theme-home-page
layout: home
footer: false
navbar: false

hero:
  text: '帮助中心'
features:
  - title: 基础工具库
    details: 可复用的工具函数
    link: /docs/utils/
  - title: 图标库
    details: 内置SVG、iconfont图标
    link: /docs/icons/
  - title: UI组件库
    details: 基础通用UI组件
    link: /docs/ui/
  - title: 核心库
    details: 低代码协议定义、模型
    link: /docs/core/
  - title: 物料库
    details: 内置ElementPlus、Antdv、UI物料
    link: /docs/materitals/
  - title: 设计器
    details: 可视化编辑页面组件
    link: /docs/designer/
  - title: 渲染器
    details: 支持DSL和源码组件渲染
    link: /docs/renderer/
  - title: 代码生成器
    details: DSL转换为源代码
    link: /docs/coder/
  - title: 脚手架
    details: 项目工程配置
    link: /docs/cli/
  - title: 本地服务
    details: 本地开发环境Vite插件
    link: /docs/local/
  - title: 应用模板
    details: 低代码应用模板
    link: /docs/apps/
---
