export const test_9 = `
<template>
  <div class="login-container">
    <div class="particles" id="particles-js"></div>
    <div class="login-box">
      <div class="glow"></div>
      <h1 class="title">系统<span class="highlight">登录</span></h1>
      <div class="line">
        <div class="dot"></div>
      </div>
      <ElForm></ElForm>
    </div>
  </div>
</template>


<script>
import {defineComponent} from 'vue'
import {ElMessage} from 'element-plus'

export default defineComponent({
  mounted() {
    ElMessage.success('ABC')
  }
})
</script>

<style lang="css" scoped>


</style>
`;
